#!/usr/bin/env python3
"""
Script para parar completamente todos os scrapers e execução automática
"""

import os
import sys
import subprocess
import signal
import psutil
from pathlib import Path

def stop_docker_containers():
    """Para containers Docker relacionados ao scraping"""
    print("🐳 Parando containers Docker...")
    
    try:
        # Parar container do scraper especificamente
        result = subprocess.run(
            ["docker", "stop", "pricenow_scraper"],
            capture_output=True,
            text=True,
            timeout=30
        )
        if result.returncode == 0:
            print("✅ Container pricenow_scraper parado")
        else:
            print("⚠️  Container pricenow_scraper não estava rodando ou não existe")
    except subprocess.TimeoutExpired:
        print("⚠️  Timeout ao parar container - forçando...")
        subprocess.run(["docker", "kill", "pricenow_scraper"], capture_output=True)
    except FileNotFoundError:
        print("⚠️  Docker não encontrado - pulando...")
    except Exception as e:
        print(f"⚠️  Erro ao parar container: {e}")

def kill_python_scrapers():
    """Mata processos Python que podem estar executando scrapers"""
    print("🐍 Procurando processos Python de scraping...")
    
    killed_count = 0
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['name'] and 'python' in proc.info['name'].lower():
                cmdline = ' '.join(proc.info['cmdline'] or [])
                
                # Verificar se é um processo de scraping
                scraping_keywords = ['scrape', 'scraper', 'main.py', 'scheduler']
                if any(keyword in cmdline.lower() for keyword in scraping_keywords):
                    # Não matar o próprio script
                    if 'stop_all_scrapers.py' not in cmdline:
                        print(f"🔪 Matando processo: PID {proc.info['pid']} - {cmdline[:100]}...")
                        proc.kill()
                        killed_count += 1
                        
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    
    if killed_count > 0:
        print(f"✅ {killed_count} processos de scraping finalizados")
    else:
        print("✅ Nenhum processo de scraping encontrado")

def check_scheduler_status():
    """Verifica se há schedulers rodando"""
    print("⏰ Verificando schedulers...")
    
    scheduler_found = False
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['cmdline']:
                cmdline = ' '.join(proc.info['cmdline'])
                if 'scheduler' in cmdline.lower() and 'python' in cmdline.lower():
                    print(f"⚠️  Scheduler encontrado: PID {proc.info['pid']}")
                    scheduler_found = True
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            pass
    
    if not scheduler_found:
        print("✅ Nenhum scheduler ativo encontrado")

def verify_configurations():
    """Verifica se as configurações estão corretas para desabilitar scrapers"""
    print("⚙️  Verificando configurações...")
    
    config_files = [
        ('config/scrapers.py', 'enabled=False'),
        ('config/settings.py', 'AUTO_SCRAPING_ENABLED'),
        ('.env.example', 'AUTO_SCRAPING_ENABLED=false'),
        ('docker-compose.yml', 'sleep, infinity')
    ]
    
    for file_path, expected_content in config_files:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if expected_content.replace(' ', '').replace(',', '') in content.replace(' ', '').replace(',', ''):
                        print(f"✅ {file_path}: Configuração correta")
                    else:
                        print(f"⚠️  {file_path}: Pode precisar de verificação")
            except Exception as e:
                print(f"❌ Erro ao ler {file_path}: {e}")
        else:
            print(f"⚠️  {file_path}: Arquivo não encontrado")

def create_stop_env():
    """Cria arquivo .env para garantir que tudo está desabilitado"""
    print("📝 Criando configurações de parada...")
    
    stop_config = """# CONFIGURAÇÃO DE PARADA - TODOS OS SCRAPERS DESABILITADOS
AUTO_SCRAPING_ENABLED=false
SCRAPERS_ENABLED=
MERCADOLIVRE_ENABLED=false
AMERICANAS_ENABLED=false
AMAZON_ENABLED=false
DROGASIL_ENABLED=false
EXAMPLE_ENABLED=false

# Para reabilitar scrapers manualmente, mude os valores acima para true
# e execute: python main.py scrape --scraper <nome>
"""
    
    try:
        with open('.env.stop', 'w', encoding='utf-8') as f:
            f.write(stop_config)
        print("✅ Arquivo .env.stop criado (copie para .env se necessário)")
    except Exception as e:
        print(f"❌ Erro ao criar .env.stop: {e}")

def main():
    """Função principal"""
    print("🛑 PARANDO TODOS OS SCRAPERS E EXECUÇÃO AUTOMÁTICA")
    print("=" * 60)
    
    # 1. Parar containers Docker
    stop_docker_containers()
    print()
    
    # 2. Matar processos Python de scraping
    kill_python_scrapers()
    print()
    
    # 3. Verificar schedulers
    check_scheduler_status()
    print()
    
    # 4. Verificar configurações
    verify_configurations()
    print()
    
    # 5. Criar configuração de parada
    create_stop_env()
    print()
    
    print("=" * 60)
    print("✅ PROCESSO DE PARADA CONCLUÍDO!")
    print()
    print("📋 RESUMO:")
    print("   • Containers Docker parados")
    print("   • Processos Python de scraping finalizados")
    print("   • Configurações verificadas")
    print("   • Arquivo .env.stop criado")
    print()
    print("🔧 PARA EXECUTAR SCRAPERS MANUALMENTE:")
    print("   python main.py scrape --scraper drogasil")
    print()
    print("🔧 PARA REABILITAR EXECUÇÃO AUTOMÁTICA:")
    print("   1. Edite .env: AUTO_SCRAPING_ENABLED=true")
    print("   2. Edite .env: DROGASIL_ENABLED=true")
    print("   3. Reinicie o sistema")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n❌ Interrompido pelo usuário")
    except Exception as e:
        print(f"\n❌ Erro durante execução: {e}")
        sys.exit(1)

# Configuração para ambiente de produção
# Copie este arquivo para .env e ajuste os valores

# Database Configuration
DATABASE_URL=********************************************************/pricenow

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000

# Playwright Configuration
PLAYWRIGHT_HEADLESS=true
PLAYWRIGHT_TIMEOUT=45000

# Scraping Configuration
SCRAPING_DELAY=3.0
MAX_RETRIES=5

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/scraper.log

# Security (para produção)
POSTGRES_PASSWORD=CHANGE_THIS_PASSWORD

# Performance
SCRAPING_CONCURRENT_LIMIT=3
API_WORKERS=4

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090

# Backup
BACKUP_ENABLED=true
BACKUP_RETENTION_DAYS=30

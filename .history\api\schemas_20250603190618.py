from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from datetime import datetime

class ScrapedDataResponse(BaseModel):
    """Schema para resposta de dados extraídos"""
    id: int
    site_name: str
    product_name: str
    price: Optional[float] = None
    original_price: Optional[float] = None
    discount_percentage: Optional[float] = None
    url: str
    image_url: Optional[str] = None
    description: Optional[str] = None
    category: Optional[str] = None
    brand: Optional[str] = None
    availability: bool = True
    rating: Optional[float] = None
    reviews_count: Optional[int] = None
    additional_data: Optional[Dict[str, Any]] = None
    scraped_at: datetime
    created_at: datetime
    updated_at: Optional[datetime] = None

    model_config = {"from_attributes": True}

class ScrapingJobResponse(BaseModel):
    """Schema para resposta de jobs de scraping"""
    id: int
    site_name: str
    status: str
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    items_scraped: int = 0
    errors_count: int = 0
    error_message: Optional[str] = None
    created_at: datetime

    model_config = {"from_attributes": True}

class SiteConfigResponse(BaseModel):
    """Schema para resposta de configuração de sites"""
    id: int
    site_name: str
    base_url: str
    is_active: bool = True
    scraping_interval: int = 3600
    selectors: Dict[str, Any]
    headers: Optional[Dict[str, Any]] = None
    cookies: Optional[Dict[str, Any]] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class ProductFilter(BaseModel):
    """Schema para filtros de produtos"""
    site_name: Optional[str] = None
    category: Optional[str] = None
    brand: Optional[str] = None
    min_price: Optional[float] = Field(None, ge=0)
    max_price: Optional[float] = Field(None, ge=0)
    availability: Optional[bool] = None
    search: Optional[str] = None

class PaginationParams(BaseModel):
    """Schema para parâmetros de paginação"""
    page: int = Field(1, ge=1, description="Número da página")
    size: int = Field(50, ge=1, le=100, description="Itens por página")

class ProductCreate(BaseModel):
    """Schema para criação de produto"""
    site_name: str
    product_name: str
    price: Optional[float] = None
    original_price: Optional[float] = None
    url: str
    image_url: Optional[str] = None
    description: Optional[str] = None
    category: Optional[str] = None
    brand: Optional[str] = None
    availability: bool = True
    rating: Optional[float] = Field(None, ge=0, le=5)
    reviews_count: Optional[int] = Field(None, ge=0)
    additional_data: Optional[Dict[str, Any]] = None

class SiteConfigCreate(BaseModel):
    """Schema para criação de configuração de site"""
    site_name: str
    base_url: str
    is_active: bool = True
    scraping_interval: int = Field(3600, ge=60)  # Mínimo 1 minuto
    selectors: Dict[str, Any]
    headers: Optional[Dict[str, Any]] = None
    cookies: Optional[Dict[str, Any]] = None

class ScrapingJobCreate(BaseModel):
    """Schema para criação de job de scraping"""
    site_name: str

class ApiResponse(BaseModel):
    """Schema genérico para respostas da API"""
    success: bool
    message: str
    data: Optional[Any] = None

class ErrorResponse(BaseModel):
    """Schema para respostas de erro"""
    error: str
    detail: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)

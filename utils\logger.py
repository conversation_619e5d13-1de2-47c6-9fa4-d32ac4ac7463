import os
import sys
from loguru import logger
from config.settings import settings

def setup_logger():
    """Configura o sistema de logging"""
    
    # Remove o logger padrão
    logger.remove()
    
    # Cria o diretório de logs se não existir
    log_dir = os.path.dirname(settings.LOG_FILE)
    if log_dir and not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # Configuração para console
    logger.add(
        sys.stdout,
        level=settings.LOG_LEVEL,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        colorize=True
    )
    
    # Configuração para arquivo
    logger.add(
        settings.LOG_FILE,
        level=settings.LOG_LEVEL,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        rotation="10 MB",
        retention="30 days",
        compression="zip"
    )
    
    return logger

# Inicializa o logger
setup_logger()

def get_logger(name: str = None):
    """Retorna uma instância do logger com nome específico"""
    if name:
        return logger.bind(name=name)
    return logger

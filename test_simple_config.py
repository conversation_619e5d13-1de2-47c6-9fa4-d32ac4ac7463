#!/usr/bin/env python3
"""
Script simples para testar configurações sem dependências externas
"""

import sys
import os

# Adicionar o diretório raiz ao path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_scraper_configs():
    """Testa as configurações dos scrapers sem dependências externas"""
    print("=== Teste Simples de Configurações ===\n")
    
    try:
        # Testar importação das configurações
        from config.scrapers import SCRAPER_CONFIGS
        
        print("1. Configurações encontradas em scrapers.py:")
        for scraper_name, config in SCRAPER_CONFIGS.items():
            enabled = config.is_enabled()
            status = "✅ HABILITADO" if enabled else "❌ DESABILITADO"
            print(f"   {scraper_name}: {status}")
            
        print("\n2. Verificação específica do Mercado Livre:")
        ml_config = SCRAPER_CONFIGS.get('mercadolivre')
        if ml_config:
            ml_enabled = ml_config.is_enabled()
            print(f"   Status: {'❌ DESABILITADO' if not ml_enabled else '⚠️ AINDA HABILITADO'}")
            print(f"   URL Base: {ml_config.base_url}")
            print(f"   Configuração enabled: {ml_config.enabled}")
            
            if not ml_enabled:
                print("   🎉 SUCESSO: Mercado Livre está desabilitado na configuração!")
            else:
                print("   ⚠️ ATENÇÃO: Mercado Livre ainda está habilitado!")
        else:
            print("   ❌ Configuração do Mercado Livre não encontrada")
            
        print("\n3. Resumo de todos os scrapers:")
        enabled_count = sum(1 for config in SCRAPER_CONFIGS.values() if config.is_enabled())
        disabled_count = len(SCRAPER_CONFIGS) - enabled_count
        
        print(f"   Total de scrapers: {len(SCRAPER_CONFIGS)}")
        print(f"   Habilitados: {enabled_count}")
        print(f"   Desabilitados: {disabled_count}")
        
        enabled_scrapers = [name for name, config in SCRAPER_CONFIGS.items() if config.is_enabled()]
        disabled_scrapers = [name for name, config in SCRAPER_CONFIGS.items() if not config.is_enabled()]
        
        if enabled_scrapers:
            print(f"   Scrapers ativos: {', '.join(enabled_scrapers)}")
        if disabled_scrapers:
            print(f"   Scrapers desabilitados: {', '.join(disabled_scrapers)}")
            
        return True
        
    except ImportError as e:
        print(f"❌ Erro ao importar configurações: {e}")
        return False
    except Exception as e:
        print(f"❌ Erro inesperado: {e}")
        return False

def test_settings_structure():
    """Testa a estrutura do arquivo de settings"""
    print("\n=== Teste da Estrutura de Settings ===\n")
    
    try:
        # Ler o arquivo settings.py como texto para verificar as configurações
        with open('config/settings.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        print("1. Verificando configurações de scrapers no settings.py:")
        
        # Verificar se as configurações estão presentes
        scraper_configs = [
            'MERCADOLIVRE_ENABLED',
            'AMERICANAS_ENABLED', 
            'AMAZON_ENABLED',
            'DROGASIL_ENABLED',
            'EXAMPLE_ENABLED',
            'SCRAPERS_ENABLED'
        ]
        
        for config in scraper_configs:
            if config in content:
                print(f"   ✅ {config} encontrado")
                # Tentar extrair o valor padrão
                for line in content.split('\n'):
                    if config in line and 'default=' in line:
                        print(f"      {line.strip()}")
                        break
            else:
                print(f"   ❌ {config} não encontrado")
                
        print("\n2. Verificando método is_scraper_enabled:")
        if 'def is_scraper_enabled' in content:
            print("   ✅ Método is_scraper_enabled encontrado")
        else:
            print("   ❌ Método is_scraper_enabled não encontrado")
            
        return True
        
    except FileNotFoundError:
        print("❌ Arquivo config/settings.py não encontrado")
        return False
    except Exception as e:
        print(f"❌ Erro ao ler settings.py: {e}")
        return False

def test_main_structure():
    """Testa a estrutura do main.py"""
    print("\n=== Teste da Estrutura do Main.py ===\n")
    
    try:
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        print("1. Verificando registro condicional de scrapers:")
        
        checks = [
            ("settings.is_scraper_enabled('mercadolivre')", "Verificação do Mercado Livre"),
            ("settings.is_scraper_enabled('drogasil')", "Verificação da Drogasil"),
            ("Scraper 'mercadolivre' desabilitado", "Log de desabilitação do ML"),
            ("Scraper 'drogasil' registrado", "Log de registro da Drogasil")
        ]
        
        for check, description in checks:
            if check in content:
                print(f"   ✅ {description}")
            else:
                print(f"   ❌ {description}")
                
        return True
        
    except FileNotFoundError:
        print("❌ Arquivo main.py não encontrado")
        return False
    except Exception as e:
        print(f"❌ Erro ao ler main.py: {e}")
        return False

if __name__ == "__main__":
    print("🔍 Testando configurações de scrapers (modo simples)...\n")
    
    success = True
    
    # Teste 1: Configurações dos scrapers
    success &= test_scraper_configs()
    
    # Teste 2: Estrutura do settings
    success &= test_settings_structure()
    
    # Teste 3: Estrutura do main
    success &= test_main_structure()
    
    print("\n" + "="*60)
    if success:
        print("✅ TODOS OS TESTES PASSARAM!")
        print("🎉 Mercado Livre foi desabilitado com sucesso!")
        print("\nPara verificar em execução, instale as dependências e execute:")
        print("   python main.py list-scrapers")
    else:
        print("❌ ALGUNS TESTES FALHARAM!")
        print("Verifique os erros acima e corrija as configurações.")

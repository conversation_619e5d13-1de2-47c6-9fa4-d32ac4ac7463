"""
Sistema de agendamento para execução automática de scrapers
"""

import asyncio
import schedule
import time
from datetime import datetime, timedelta
from typing import Dict, List, Callable
from utils.logger import get_logger
from config.settings import settings

logger = get_logger("scheduler")

class ScrapingScheduler:
    """Gerenciador de agendamento de scrapers"""
    
    def __init__(self):
        self.jobs = {}
        self.running = False
        self.scraper_manager = None
    
    def set_scraper_manager(self, manager):
        """Define o gerenciador de scrapers"""
        self.scraper_manager = manager
    
    def add_daily_job(self, scraper_name: str, time_str: str):
        """Adiciona job diário para um scraper
        
        Args:
            scraper_name: Nome do scraper
            time_str: Horário no formato "HH:MM" (ex: "14:30")
        """
        if not self.scraper_manager:
            logger.error("Scraper manager não configurado")
            return
        
        job = schedule.every().day.at(time_str).do(
            self._run_scraper_async, scraper_name
        )
        
        self.jobs[f"{scraper_name}_daily_{time_str}"] = job
        logger.info(f"Job diário agendado: {scraper_name} às {time_str}")
    
    def add_hourly_job(self, scraper_name: str, interval: int = 1):
        """Adiciona job de execução a cada X horas
        
        Args:
            scraper_name: Nome do scraper
            interval: Intervalo em horas
        """
        if not self.scraper_manager:
            logger.error("Scraper manager não configurado")
            return
        
        job = schedule.every(interval).hours.do(
            self._run_scraper_async, scraper_name
        )
        
        self.jobs[f"{scraper_name}_hourly_{interval}h"] = job
        logger.info(f"Job de {interval}h agendado: {scraper_name}")
    
    def add_interval_job(self, scraper_name: str, minutes: int):
        """Adiciona job de execução a cada X minutos
        
        Args:
            scraper_name: Nome do scraper
            minutes: Intervalo em minutos
        """
        if not self.scraper_manager:
            logger.error("Scraper manager não configurado")
            return
        
        job = schedule.every(minutes).minutes.do(
            self._run_scraper_async, scraper_name
        )
        
        self.jobs[f"{scraper_name}_interval_{minutes}m"] = job
        logger.info(f"Job de {minutes}min agendado: {scraper_name}")
    
    def remove_job(self, job_name: str):
        """Remove um job específico"""
        if job_name in self.jobs:
            schedule.cancel_job(self.jobs[job_name])
            del self.jobs[job_name]
            logger.info(f"Job removido: {job_name}")
        else:
            logger.warning(f"Job não encontrado: {job_name}")
    
    def remove_scraper_jobs(self, scraper_name: str):
        """Remove todos os jobs de um scraper específico"""
        jobs_to_remove = [
            job_name for job_name in self.jobs.keys() 
            if job_name.startswith(scraper_name)
        ]
        
        for job_name in jobs_to_remove:
            self.remove_job(job_name)
        
        logger.info(f"Removidos {len(jobs_to_remove)} jobs do scraper {scraper_name}")
    
    def list_jobs(self) -> List[Dict]:
        """Lista todos os jobs agendados"""
        jobs_info = []
        
        for job_name, job in self.jobs.items():
            next_run = job.next_run
            jobs_info.append({
                "name": job_name,
                "next_run": next_run.isoformat() if next_run else None,
                "interval": str(job.interval) if hasattr(job, 'interval') else None
            })
        
        return jobs_info
    
    def _run_scraper_async(self, scraper_name: str):
        """Executa scraper de forma assíncrona"""
        try:
            logger.info(f"Executando scraper agendado: {scraper_name}")
            
            # Cria novo loop de eventos para execução assíncrona
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                loop.run_until_complete(
                    self.scraper_manager.run_scraper(scraper_name)
                )
                logger.info(f"Scraper agendado concluído: {scraper_name}")
            finally:
                loop.close()
                
        except Exception as e:
            logger.error(f"Erro ao executar scraper agendado {scraper_name}: {e}")
    
    def start(self):
        """Inicia o scheduler"""
        if self.running:
            logger.warning("Scheduler já está rodando")
            return
        
        self.running = True
        logger.info("Scheduler iniciado")
        
        try:
            while self.running:
                schedule.run_pending()
                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("Scheduler interrompido pelo usuário")
        finally:
            self.running = False
    
    def stop(self):
        """Para o scheduler"""
        self.running = False
        logger.info("Scheduler parado")
    
    def setup_default_schedules(self):
        """Configura agendamentos padrão - TODOS DESABILITADOS"""
        logger.info("⚠️  AGENDAMENTOS AUTOMÁTICOS DESABILITADOS")
        logger.info("Para executar scrapers, use: python main.py scrape --scraper <nome>")

        # TODOS OS AGENDAMENTOS FORAM DESABILITADOS
        # Para reabilitar, descomente as linhas abaixo:

        # # Mercado Livre: a cada 2 horas
        # self.add_hourly_job("mercadolivre", 2)

        # # Americanas: a cada 3 horas
        # self.add_hourly_job("americanas", 3)

        # # Exemplo: diário às 08:00
        # self.add_daily_job("example", "08:00")

        logger.info("Sistema de agendamento em modo manual")

class AsyncScheduler:
    """Scheduler assíncrono para melhor integração com aplicações async"""
    
    def __init__(self):
        self.tasks = {}
        self.running = False
        self.scraper_manager = None
    
    def set_scraper_manager(self, manager):
        """Define o gerenciador de scrapers"""
        self.scraper_manager = manager
    
    async def add_periodic_task(self, scraper_name: str, interval_seconds: int):
        """Adiciona tarefa periódica
        
        Args:
            scraper_name: Nome do scraper
            interval_seconds: Intervalo em segundos
        """
        if scraper_name in self.tasks:
            self.tasks[scraper_name].cancel()
        
        task = asyncio.create_task(
            self._periodic_scraper(scraper_name, interval_seconds)
        )
        self.tasks[scraper_name] = task
        
        logger.info(f"Tarefa periódica adicionada: {scraper_name} a cada {interval_seconds}s")
    
    async def _periodic_scraper(self, scraper_name: str, interval_seconds: int):
        """Executa scraper periodicamente"""
        while self.running:
            try:
                logger.info(f"Executando scraper periódico: {scraper_name}")
                await self.scraper_manager.run_scraper(scraper_name)
                logger.info(f"Scraper periódico concluído: {scraper_name}")
            except Exception as e:
                logger.error(f"Erro no scraper periódico {scraper_name}: {e}")
            
            await asyncio.sleep(interval_seconds)
    
    async def start(self):
        """Inicia o scheduler assíncrono"""
        self.running = True
        logger.info("Scheduler assíncrono iniciado")
        
        # Aguarda todas as tarefas
        if self.tasks:
            await asyncio.gather(*self.tasks.values(), return_exceptions=True)
    
    def stop(self):
        """Para o scheduler assíncrono"""
        self.running = False
        
        # Cancela todas as tarefas
        for task in self.tasks.values():
            task.cancel()
        
        logger.info("Scheduler assíncrono parado")
    
    def remove_task(self, scraper_name: str):
        """Remove tarefa específica"""
        if scraper_name in self.tasks:
            self.tasks[scraper_name].cancel()
            del self.tasks[scraper_name]
            logger.info(f"Tarefa removida: {scraper_name}")
    
    def list_tasks(self) -> List[str]:
        """Lista tarefas ativas"""
        return list(self.tasks.keys())

# Instância global do scheduler
scheduler = ScrapingScheduler()
async_scheduler = AsyncScheduler()

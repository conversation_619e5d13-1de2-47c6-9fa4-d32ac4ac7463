import re
from typing import List, Dict, Any
from core.base_scraper import BaseScraper

class DrogasilScraper(BaseScraper):
    """Scraper para o site Drogasil - busca por EAN"""

    def __init__(self, eans: List[str] = None):
        super().__init__("drogasil")
        self.base_url = "https://www.drogasil.com.br"
        self.eans = eans or ["7896004703398"]
    
    def get_urls_to_scrape(self) -> List[str]:
        return [f"{self.base_url}/search?w={ean}" for ean in self.eans]

    async def setup_browser(self):
        """Configuração básica do browser"""
        await super().setup_browser()

        # User agent realista
        user_agent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'

        # Headers básicos
        await self.page.set_extra_http_headers({
            'User-Agent': user_agent,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
            'Accept-Language': 'pt-BR,pt;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br'
        })

    async def scrape(self) -> List[Dict[str, Any]]:
        """Extrai dados da página de resultados do Drogasil"""
        products = []

        try:
            # Aguardar produtos carregarem
            await self.page.wait_for_selector("article[data-item-id]", timeout=15000)
            product_elements = await self.page.query_selector_all("article[data-item-id]")
            self.logger.info(f"Encontrados {len(product_elements)} produtos na página")

            for element in product_elements:
                product_data = await self.extract_product_data(element)
                if product_data:
                    products.append(product_data)
                    self.logger.info(f"Produto extraído: {product_data['product_name']}")

        except Exception as e:
            self.logger.error(f"Erro ao fazer scraping da página: {e}")

        return products
    

    
    async def extract_product_data(self, element) -> Dict[str, Any]:
        """Extrai dados de um produto específico do Drogasil"""
        try:
            # Nome do produto
            name_element = await element.query_selector("h2 a")
            if not name_element:
                return None
            product_name = (await name_element.inner_text()).strip()

            # URL do produto
            product_url = await name_element.get_attribute("href")
            if product_url and not product_url.startswith("http"):
                product_url = f"{self.base_url}{product_url}"

            # Preço
            price = None
            price_element = await element.query_selector("[data-testid='price']")
            if price_element:
                price_text = await price_element.inner_text()
                price = self.extract_price(price_text)

            # Imagem
            image_url = None
            img_element = await element.query_selector("[data-testid='product-image']")
            if img_element:
                image_url = await img_element.get_attribute("src")

            # Marca (extrair do nome do produto)
            brand = None
            if product_name:
                if "EMS" in product_name:
                    brand = "EMS"
                elif "Genérico" in product_name:
                    brand = "Genérico"

            return {
                "product_name": product_name,
                "price": price,
                "url": product_url,
                "image_url": image_url,
                "category": "Farmácia",
                "brand": brand,
                "availability": True,
                "additional_data": {
                    "scraped_from": self.page.url,
                    "site_type": "farmacia"
                }
            }
        except Exception as e:
            self.logger.error(f"Erro ao extrair dados do produto: {e}")
            return None
    
    def extract_price(self, price_text: str) -> float:
        """Extrai valor numérico do preço"""
        if not price_text:
            return None
        try:
            price_clean = re.sub(r"[^\d,.]", "", price_text)
            price_clean = price_clean.replace(",", ".")
            if price_clean.count(".") > 1:
                parts = price_clean.split(".")
                price_clean = "".join(parts[:-1]) + "." + parts[-1]
            return float(price_clean)
        except:
            return None
    


import re
import urllib.parse
from typing import List, Dict, Any
from core.base_scraper import BaseScraper

class DrogasilScraper(BaseScraper):
    """Scraper para Drogasil - versão com dados simulados devido a proteções anti-bot"""
    
    def __init__(self, eans: List[str] = None):
        super().__init__("drogasil")
        self.base_url = "https://www.drogasil.com.br"
        self.eans = eans or ["7896004703398"]
        self.use_mock_data = False  # Agora vamos usar dados reais baseados no template
    
    def get_urls_to_scrape(self) -> List[str]:
        return [f"{self.base_url}/search?w={ean}" for ean in self.eans]

    async def setup_browser(self):
        """Configuração avançada do browser para evitar detecção"""
        await super().setup_browser()

        # User agents realistas e atualizados
        realistic_user_agents = [
            # Chrome Windows mais recente
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            # Chrome macOS
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            # Firefox Windows
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:122.0) Gecko/20100101 Firefox/122.0',
            # Edge Windows
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
            # Safari macOS
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2.1 Safari/605.1.15'
        ]

        # Escolher um user agent aleatório
        import random
        selected_ua = random.choice(realistic_user_agents)
        self.logger.info(f"🎭 User Agent selecionado: {selected_ua[:50]}...")

        # Configurações anti-detecção mais avançadas
        await self.page.add_init_script(f"""
            // Remove webdriver property
            Object.defineProperty(navigator, 'webdriver', {{
                get: () => undefined,
            }});

            // Override user agent
            Object.defineProperty(navigator, 'userAgent', {{
                get: () => '{selected_ua}',
            }});

            // Mock plugins realistas
            Object.defineProperty(navigator, 'plugins', {{
                get: () => [
                    {{name: 'Chrome PDF Plugin', filename: 'internal-pdf-viewer'}},
                    {{name: 'Chrome PDF Viewer', filename: 'mhjfbmdgcfjbbpaeojofohoefgiehjai'}},
                    {{name: 'Native Client', filename: 'internal-nacl-plugin'}}
                ],
            }});

            // Mock languages realistas
            Object.defineProperty(navigator, 'languages', {{
                get: () => ['pt-BR', 'pt', 'en-US', 'en'],
            }});

            // Mock chrome object
            window.chrome = {{
                runtime: {{}},
                loadTimes: function() {{}},
                csi: function() {{}},
                app: {{}}
            }};

            // Mock permissions
            const originalQuery = window.navigator.permissions.query;
            window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                    Promise.resolve({{ state: 'default' }}) :
                    originalQuery(parameters)
            );

            // Remove automation indicators
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;

            // Mock screen properties
            Object.defineProperty(screen, 'width', {{get: () => 1920}});
            Object.defineProperty(screen, 'height', {{get: () => 1080}});
            Object.defineProperty(screen, 'availWidth', {{get: () => 1920}});
            Object.defineProperty(screen, 'availHeight', {{get: () => 1040}});
        """)

        # Headers mais realistas baseados no user agent
        headers = {
            'User-Agent': selected_ua,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1'
        }

        # Headers específicos por browser
        if 'Chrome' in selected_ua:
            headers.update({
                'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="121", "Google Chrome";v="121"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"' if 'Windows' in selected_ua else '"macOS"'
            })

        await self.page.set_extra_http_headers(headers)

    async def navigate_to_page(self, url: str, wait_for: str = None):
        """Navegação personalizada simulando usuário real"""
        try:
            self.logger.info(f"🌐 Simulando navegação humana para buscar EAN...")

            # Extrair EAN da URL
            ean = url.split('w=')[-1] if 'w=' in url else '7896004703398'
            self.logger.info(f"🔍 EAN a buscar: {ean}")

            # 1. Navegar para página inicial (comportamento humano)
            self.logger.info("🏠 Visitando página inicial...")
            try:
                await self.page.goto(self.base_url, timeout=60000, wait_until='domcontentloaded')
                await self.page.wait_for_timeout(3000)
                self.logger.info("✅ Página inicial carregada")
            except Exception as e:
                self.logger.error(f"❌ Erro na página inicial: {e}")
                await self.capture_error_screenshot("erro_pagina_inicial")
                raise

            # 2. Procurar campo de busca e simular digitação
            self.logger.info("🔍 Procurando campo de busca...")
            try:
                # Seletores possíveis para campo de busca
                search_selectors = [
                    'input[type="search"]',
                    'input[placeholder*="busca"]',
                    'input[placeholder*="produto"]',
                    'input[name="search"]',
                    'input[id*="search"]',
                    '.search-input',
                    '#search-input'
                ]

                search_input = None
                for selector in search_selectors:
                    try:
                        search_input = await self.page.query_selector(selector)
                        if search_input:
                            self.logger.info(f"✅ Campo de busca encontrado: {selector}")
                            break
                    except:
                        continue

                if not search_input:
                    self.logger.warning("⚠️ Campo de busca não encontrado, tentando URL direta...")
                    # Fallback para URL direta
                    await self.page.goto(url, timeout=60000, wait_until='domcontentloaded')
                    await self.page.wait_for_timeout(3000)
                else:
                    # 3. Simular digitação humana
                    self.logger.info(f"⌨️ Digitando EAN: {ean}")
                    await search_input.click()
                    await self.page.wait_for_timeout(500)

                    # Digitar caractere por caractere (mais humano)
                    for char in ean:
                        await search_input.type(char)
                        await self.page.wait_for_timeout(100)  # 100ms entre caracteres

                    # 4. Procurar botão de busca e clicar
                    self.logger.info("🔍 Procurando botão de busca...")
                    search_button_selectors = [
                        'button[type="submit"]',
                        'input[type="submit"]',
                        '.search-button',
                        '[aria-label*="busca"]',
                        'button:has-text("Buscar")',
                        'button:has-text("Pesquisar")'
                    ]

                    search_button = None
                    for selector in search_button_selectors:
                        try:
                            search_button = await self.page.query_selector(selector)
                            if search_button:
                                self.logger.info(f"✅ Botão de busca encontrado: {selector}")
                                break
                        except:
                            continue

                    if search_button:
                        await search_button.click()
                        self.logger.info("🔍 Busca executada via botão")
                    else:
                        # Tentar Enter no campo de busca
                        await search_input.press('Enter')
                        self.logger.info("🔍 Busca executada via Enter")

                    # Aguardar resultados carregarem
                    await self.page.wait_for_timeout(5000)

            except Exception as e:
                self.logger.error(f"❌ Erro na busca: {e}")
                await self.capture_error_screenshot("erro_busca")
                raise

            # Verificar se a página carregou corretamente
            title = await self.page.title()
            self.logger.info(f"📄 Título da página: {title}")

            if "Access Denied" in title or "Forbidden" in title:
                self.logger.error("🚫 Acesso negado detectado")
                await self.capture_error_screenshot("access_denied")
                raise Exception("Site bloqueou o acesso - Access Denied")

            # Tentar fechar possíveis modais
            try:
                close_selectors = [
                    '.modal-close',
                    '.popup-close',
                    '.close-button',
                    '[aria-label="Fechar"]',
                    '[aria-label="Close"]',
                    '.cookie-accept',
                    '.accept-cookies',
                    '.lgpd-accept',
                    '.consent-accept'
                ]

                for selector in close_selectors:
                    close_button = await self.page.query_selector(selector)
                    if close_button:
                        self.logger.info(f"🔘 Fechando modal: {selector}")
                        await close_button.click()
                        await self.page.wait_for_timeout(1000)
                        break
            except Exception as modal_error:
                self.logger.warning(f"⚠️ Erro ao fechar modais: {modal_error}")

            self.logger.info(f"✅ Navegação concluída para: {url}")

        except Exception as e:
            self.logger.error(f"❌ Erro ao navegar para {url}: {e}")
            # Capturar screenshot final do erro
            await self.capture_error_screenshot("erro_navegacao_final")
            raise

    async def capture_error_screenshot(self, error_type: str):
        """Captura screenshot quando ocorre erro"""
        try:
            timestamp = __import__('datetime').datetime.now().strftime("%Y%m%d_%H%M%S")
            screenshot_path = f"error_{error_type}_{timestamp}.png"
            await self.page.screenshot(path=screenshot_path, full_page=True)
            self.logger.info(f"📸 Screenshot de erro salvo: {screenshot_path}")

            # Também salvar HTML para análise
            html_path = f"error_{error_type}_{timestamp}.html"
            content = await self.page.content()
            with open(html_path, 'w', encoding='utf-8') as f:
                f.write(content)
            self.logger.info(f"💾 HTML de erro salvo: {html_path}")

        except Exception as screenshot_error:
            self.logger.error(f"❌ Erro ao capturar screenshot: {screenshot_error}")

    async def load_template_for_testing(self):
        """Carrega o template HTML local para testes"""
        try:
            # Carrega o template HTML local
            with open('/app/drogasil-template.html', 'r', encoding='utf-8') as f:
                template_html = f.read()

            # Cria uma página HTML completa
            full_html = f"""
            <!DOCTYPE html>
            <html>
            <head><title>Drogasil Template Test</title></head>
            <body>
                <div id="products-container">
                    {template_html}
                </div>
            </body>
            </html>
            """

            # Carrega o HTML na página
            await self.page.set_content(full_html)
            self.logger.info("Template HTML carregado para testes")
            return True

        except Exception as e:
            self.logger.error(f"Erro ao carregar template: {e}")
            return False
    
    async def scrape(self) -> List[Dict[str, Any]]:
        """Extrai dados da página de resultados do Drogasil - apenas dados reais"""
        products = []

        try:
            self.logger.info("🔍 Extraindo dados reais do site...")

            # Primeiro vamos verificar o que realmente está na página
            page_title = await self.page.title()
            self.logger.info(f"📄 Título da página: {page_title}")

            # Verificar se a página carregou corretamente
            if "Access Denied" in page_title:
                self.logger.error("❌ Site bloqueou o acesso - Access Denied")
                return products

            # Aguardar um tempo maior para JavaScript carregar
            self.logger.info("⏳ Aguardando JavaScript carregar...")
            await self.page.wait_for_timeout(10000)  # 10 segundos

            # Verificar se há produtos na página
            page_content = await self.page.content()
            if "nenhum resultado" in page_content.lower() or "sem resultado" in page_content.lower():
                self.logger.warning("⚠️ Nenhum produto encontrado para este EAN")
                return products

            # Tentar diferentes seletores para encontrar produtos
            selectors_to_try = [
                "article[data-item-id]",
                "article",
                "[data-item-id]",
                ".product-card",
                ".product-item",
                "[class*='product']"
            ]

            product_elements = []
            for selector in selectors_to_try:
                try:
                    self.logger.info(f"🔍 Tentando seletor: {selector}")
                    await self.page.wait_for_selector(selector, timeout=5000)
                    elements = await self.page.query_selector_all(selector)
                    if elements:
                        self.logger.info(f"✅ Encontrados {len(elements)} elementos com {selector}")
                        product_elements = elements
                        break
                except:
                    self.logger.info(f"❌ Seletor {selector} não encontrou elementos")
                    continue

            if not product_elements:
                self.logger.warning("❌ Nenhum elemento de produto encontrado com nenhum seletor")
                await self.capture_error_screenshot("nenhum_produto_encontrado")
                return products

            # Extrair dados dos produtos encontrados
            for i, element in enumerate(product_elements):
                self.logger.info(f"🔍 Extraindo dados do produto {i+1}/{len(product_elements)}")
                product_data = await self.extract_product_data(element)
                if product_data:
                    products.append(product_data)
                    self.logger.info(f"✅ Produto extraído: {product_data['product_name']}")
                else:
                    self.logger.warning(f"⚠️ Produto {i+1} ignorado - dados incompletos")

        except Exception as e:
            self.logger.error(f"❌ Erro ao extrair dados reais: {e}")

        self.logger.info(f"📊 Total de produtos extraídos: {len(products)}")
        return products
    
    async def extract_product_data(self, element) -> Dict[str, Any]:
        """Extrai dados de um produto específico do Drogasil baseado no template real"""
        try:
            # Nome do produto - baseado no template (linha 41-44)
            name_element = await element.query_selector("h2.sc-d50cb26-1 a")
            if not name_element:
                # Fallback para seletor mais genérico
                name_element = await element.query_selector("h2 a")

            if not name_element:
                self.logger.warning("Nome do produto não encontrado")
                return None

            product_name = (await name_element.inner_text()).strip()

            # URL do produto - mesmo elemento do nome
            product_url = await name_element.get_attribute("href")
            if product_url and not product_url.startswith("http"):
                product_url = f"{self.base_url}{product_url}"

            # Preço - baseado no template (linha 101)
            price = None
            price_element = await element.query_selector("[data-testid='price']")
            if price_element:
                price_text = await price_element.inner_text()
                price = self.extract_price(price_text)

            # Imagem - baseado no template (linha 15)
            image_url = None
            img_element = await element.query_selector("[data-testid='product-image']")
            if img_element:
                # Tenta primeiro src, depois srcset
                image_url = await img_element.get_attribute("src")
                if not image_url:
                    srcset = await img_element.get_attribute("srcset")
                    if srcset:
                        # Pega a primeira URL do srcset
                        image_url = srcset.split(' ')[0]
            
            # Preço original (se houver desconto)
            original_price = None
            original_price_element = await element.query_selector(".price-original, .old-price")
            if original_price_element:
                original_price_text = await original_price_element.inner_text()
                original_price = self.extract_price(original_price_text)
            
            # Desconto percentual
            discount_percentage = None
            if price and original_price and original_price > price:
                discount_percentage = round(((original_price - price) / original_price) * 100, 1)
            
            # Avaliações - baseado no template (linhas 66-72)
            rating = None
            reviews_count = None

            # Extrair rating das estrelas - baseado no template
            rating_element = await element.query_selector(".v_ratings__stars__enabled")
            if rating_element:
                style = await rating_element.get_attribute("style")
                if style and "width:" in style:
                    # No template: style="width: 80px" = 4 estrelas (80/100 * 5)
                    width_match = re.search(r"width:\s*(\d+)px", style)
                    if width_match:
                        width = int(width_match.group(1))
                        rating = round((width / 100) * 5, 1)

            # Extrair número de avaliações - baseado no template
            reviews_element = await element.query_selector(".v_ratings__stars__reviewsCount")
            if reviews_element:
                reviews_text = await reviews_element.inner_text()
                # No template: "(12)" -> extrair o número
                reviews_count = self.extract_number(reviews_text)
            
            # Marca - extrair do nome do produto baseado no template
            brand = None
            if product_name:
                # No template: "Dipirona Monoidratada 500mg 10 comprimidos EMS Genérico"
                if "EMS" in product_name:
                    brand = "EMS"
                elif "Genérico" in product_name:
                    brand = "Genérico"
                else:
                    # Tenta extrair marca de outras formas
                    words = product_name.split()
                    for word in words:
                        if word.isupper() and len(word) > 2:
                            brand = word
                            break
            
            # EAN da busca atual
            current_ean = self.extract_ean_from_url(self.page.url) if hasattr(self.page, 'url') else self.eans[0]

            return {
                "product_name": product_name,
                "price": price,
                "original_price": original_price,
                "discount_percentage": discount_percentage,
                "url": product_url,
                "image_url": image_url,
                "description": None,  # Drogasil não mostra descrição na listagem
                "category": "Farmácia",
                "brand": brand,
                "availability": True,  # Assume disponível se está na listagem
                "rating": rating,
                "reviews_count": reviews_count,
                "additional_data": {
                    "ean_searched": current_ean,
                    "scraped_from": getattr(self.page, 'url', f"{self.base_url}/search?w={current_ean}"),
                    "site_type": "farmacia",
                    "extraction_method": "template_based",  # Indica que usa template
                    "template_data": False  # Será marcado como True se vier do template
                }
            }
        except Exception as e:
            self.logger.error(f"Erro ao extrair dados do produto: {e}")
            return None
    
    def extract_price(self, price_text: str) -> float:
        """Extrai valor numérico do preço"""
        if not price_text:
            return None
        try:
            price_clean = re.sub(r"[^\d,.]", "", price_text)
            price_clean = price_clean.replace(",", ".")
            if price_clean.count(".") > 1:
                parts = price_clean.split(".")
                price_clean = "".join(parts[:-1]) + "." + parts[-1]
            return float(price_clean)
        except:
            return None
    
    def extract_number(self, text: str) -> int:
        """Extrai número de um texto"""
        if not text:
            return None
        try:
            numbers = re.findall(r"\d+", text)
            if numbers:
                return int(numbers[0])
        except:
            return None
    
    def extract_ean_from_url(self, url: str) -> str:
        """Extrai EAN da URL de busca"""
        try:
            parsed_url = urllib.parse.urlparse(url)
            query_params = urllib.parse.parse_qs(parsed_url.query)
            if "w" in query_params:
                return query_params["w"][0]
        except:
            return None

#!/usr/bin/env python3
"""
Script para corrigir o container do scraper (sem removê-lo)
"""

import subprocess
import time
import sys
import os

def run_command(cmd, description="", ignore_errors=False, timeout=30):
    """Executa comando e mostra resultado"""
    print(f"🔧 {description}")
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=timeout)
        if result.returncode == 0 or ignore_errors:
            print(f"✅ {description} - OK")
            if result.stdout.strip():
                print(f"   Output: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ {description} - ERRO")
            if result.stderr.strip():
                print(f"   Erro: {result.stderr.strip()}")
            return False
    except subprocess.TimeoutExpired:
        print(f"⏰ {description} - TIMEOUT")
        return False
    except Exception as e:
        print(f"❌ {description} - EXCEÇÃO: {e}")
        return False

def check_container_status():
    """Verifica status atual do container"""
    print("📊 Verificando status do container...")
    
    # Verificar se container existe
    result = subprocess.run("docker ps -a --filter name=pricenow_scraper --format '{{.Status}}'", 
                          shell=True, capture_output=True, text=True)
    
    if result.stdout.strip():
        status = result.stdout.strip()
        print(f"   Container pricenow_scraper: {status}")
        
        # Verificar se está reiniciando
        if "Restarting" in status:
            print("⚠️  Container está reiniciando constantemente!")
            return "restarting"
        elif "Up" in status:
            print("✅ Container está rodando")
            return "running"
        elif "Exited" in status:
            print("⚠️  Container parou")
            return "stopped"
    else:
        print("❌ Container não encontrado")
        return "not_found"

def stop_scraper_container():
    """Para o container do scraper"""
    print("🛑 Parando container do scraper...")
    return run_command("docker stop pricenow_scraper", "Parando pricenow_scraper", ignore_errors=True)

def rebuild_scraper_container():
    """Reconstrói o container do scraper"""
    print("🔨 Reconstruindo container do scraper...")
    
    # Rebuild com cache limpo
    if not run_command("docker-compose build --no-cache scraper", "Rebuild do scraper", timeout=300):
        return False
    
    return True

def start_scraper_container():
    """Inicia o container do scraper"""
    print("🚀 Iniciando container do scraper...")
    return run_command("docker-compose up -d scraper", "Iniciando scraper")

def check_scraper_logs():
    """Verifica logs do scraper"""
    print("📋 Verificando logs do scraper...")
    
    # Aguardar um pouco para logs aparecerem
    time.sleep(5)
    
    result = subprocess.run("docker logs pricenow_scraper --tail 20", 
                          shell=True, capture_output=True, text=True)
    
    if result.stdout:
        print("📄 Últimos logs do scraper:")
        for line in result.stdout.split('\n')[-10:]:
            if line.strip():
                print(f"   {line}")
    
    return True

def test_manual_execution():
    """Testa execução manual de scraper"""
    print("🧪 Testando execução manual...")
    
    # Testar comando list-scrapers
    result = subprocess.run("docker exec pricenow_scraper python main.py list-scrapers", 
                          shell=True, capture_output=True, text=True, timeout=30)
    
    if result.returncode == 0:
        print("✅ Comando list-scrapers funcionou")
        print(f"   Output: {result.stdout.strip()}")
        return True
    else:
        print("❌ Comando list-scrapers falhou")
        if result.stderr:
            print(f"   Erro: {result.stderr.strip()}")
        return False

def monitor_container():
    """Monitora container por alguns segundos"""
    print("👀 Monitorando container por 30 segundos...")
    
    for i in range(6):  # 6 x 5 segundos = 30 segundos
        time.sleep(5)
        
        result = subprocess.run("docker ps --filter name=pricenow_scraper --format '{{.Status}}'", 
                              shell=True, capture_output=True, text=True)
        
        if result.stdout.strip():
            status = result.stdout.strip()
            print(f"   [{i*5+5}s] Status: {status}")
            
            if "Restarting" in status:
                print("❌ Container ainda está reiniciando!")
                return False
        else:
            print(f"   [{i*5+5}s] Container não encontrado")
            return False
    
    print("✅ Container estável por 30 segundos")
    return True

def main():
    """Função principal"""
    print("🔧 CORREÇÃO DO CONTAINER DO SCRAPER")
    print("=" * 50)
    
    # 1. Verificar status atual
    status = check_container_status()
    
    if status == "not_found":
        print("❌ Container não existe - execute docker-compose up -d primeiro")
        return False
    
    # 2. Se estiver reiniciando, parar
    if status == "restarting":
        print("🛑 Container reiniciando - parando para correção...")
        stop_scraper_container()
        time.sleep(5)
    
    # 3. Parar container se estiver rodando
    if status in ["running", "restarting"]:
        stop_scraper_container()
        time.sleep(3)
    
    # 4. Reconstruir container
    print("\n🔨 Reconstruindo container com correções...")
    if not rebuild_scraper_container():
        print("❌ Falha ao reconstruir container")
        return False
    
    # 5. Iniciar container
    print("\n🚀 Iniciando container corrigido...")
    if not start_scraper_container():
        print("❌ Falha ao iniciar container")
        return False
    
    # 6. Verificar logs
    print("\n📋 Verificando logs...")
    check_scraper_logs()
    
    # 7. Monitorar estabilidade
    print("\n👀 Monitorando estabilidade...")
    if not monitor_container():
        print("❌ Container não está estável")
        return False
    
    # 8. Testar execução manual
    print("\n🧪 Testando funcionalidade...")
    if not test_manual_execution():
        print("⚠️  Execução manual com problemas, mas container está estável")
    
    # 9. Status final
    print("\n📊 Status final:")
    check_container_status()
    
    print("\n" + "=" * 50)
    print("✅ CORREÇÃO CONCLUÍDA!")
    print()
    print("📋 CONTAINER CORRIGIDO:")
    print("   • Container não reinicia mais constantemente")
    print("   • Modo standby ativo (scrapers desabilitados)")
    print("   • Inicialização robusta com timeouts")
    print("   • Tratamento de erros melhorado")
    print()
    print("🔧 PARA USAR:")
    print("   # Verificar status")
    print("   docker ps | grep scraper")
    print()
    print("   # Ver logs")
    print("   docker logs pricenow_scraper")
    print()
    print("   # Executar scraper manualmente")
    print("   docker exec pricenow_scraper python main.py scrape --scraper drogasil")
    print()
    print("🎯 PRÓXIMOS PASSOS:")
    print("   1. Container deve ficar estável em modo standby")
    print("   2. Execute scrapers manualmente quando necessário")
    print("   3. Monitore logs para garantir estabilidade")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n❌ Interrompido pelo usuário")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Erro inesperado: {e}")
        sys.exit(1)

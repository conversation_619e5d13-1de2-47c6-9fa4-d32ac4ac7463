import re
import urllib.parse
from typing import List, Dict, Any
from core.base_scraper import BaseScraper

class DrogasilScraper(BaseScraper):
    """Scraper para Drogasil - versão com dados simulados devido a proteções anti-bot"""
    
    def __init__(self, eans: List[str] = None):
        super().__init__("drogasil")
        self.base_url = "https://www.drogasil.com.br"
        self.eans = eans or ["7896004703398"]
        self.use_mock_data = True  # Flag para usar dados simulados
    
    def get_urls_to_scrape(self) -> List[str]:
        return [f"{self.base_url}/search?w={ean}" for ean in self.eans]
    
    async def scrape(self) -> List[Dict[str, Any]]:
        """Extrai dados da página de resultados do Drogasil"""
        products = []
        
        if self.use_mock_data:
            # Dados simulados baseados na estrutura real do Drogasil
            self.logger.info("⚠️ Usando dados simulados devido a proteções anti-bot do site")
            
            mock_products = [
                {
                    "product_name": "Dipirona Sódica 500mg EMS - 10 Comprimidos",
                    "price": 8.99,
                    "original_price": 12.50,
                    "discount_percentage": 28.1,
                    "url": f"{self.base_url}/dipirona-sodica-500mg-ems-10-comprimidos",
                    "image_url": "https://www.drogasil.com.br/images/dipirona-ems.jpg",
                    "description": None,
                    "category": "Farmácia",
                    "brand": "EMS",
                    "availability": True,
                    "rating": 4.5,
                    "reviews_count": 127,
                    "additional_data": {
                        "ean_searched": "7896004703398",
                        "scraped_from": f"{self.base_url}/search?w=7896004703398",
                        "site_type": "farmacia",
                        "mock_data": True
                    }
                },
                {
                    "product_name": "Dipirona Sódica 500mg Genérico - 20 Comprimidos",
                    "price": 15.90,
                    "original_price": None,
                    "discount_percentage": None,
                    "url": f"{self.base_url}/dipirona-sodica-500mg-generico-20-comprimidos",
                    "image_url": "https://www.drogasil.com.br/images/dipirona-generico.jpg",
                    "description": None,
                    "category": "Farmácia",
                    "brand": "Genérico",
                    "availability": True,
                    "rating": 4.2,
                    "reviews_count": 89,
                    "additional_data": {
                        "ean_searched": "7896004703398",
                        "scraped_from": f"{self.base_url}/search?w=7896004703398",
                        "site_type": "farmacia",
                        "mock_data": True
                    }
                }
            ]
            
            for product in mock_products:
                products.append(product)
                self.logger.info(f"Produto simulado extraído: {product['product_name']}")
            
            return products
        
        # Código real de scraping (atualmente bloqueado)
        try:
            await self.page.wait_for_selector("article[data-item-id]", timeout=15000)
            product_elements = await self.page.query_selector_all("article[data-item-id]")
            self.logger.info(f"Encontrados {len(product_elements)} produtos na página")
            
            for element in product_elements:
                product_data = await self.extract_product_data(element)
                if product_data:
                    products.append(product_data)
                    self.logger.info(f"Produto extraído: {product_data['product_name']}")
        except Exception as e:
            self.logger.error(f"Erro ao fazer scraping da página: {e}")
            self.logger.info("Tentando usar dados simulados...")
            return await self.scrape()  # Recursão para usar mock data
        
        return products
    
    async def extract_product_data(self, element) -> Dict[str, Any]:
        """Extrai dados de um produto específico do Drogasil"""
        try:
            # Nome do produto
            name_element = await element.query_selector("h2 a")
            if not name_element:
                return None
            product_name = (await name_element.inner_text()).strip()
            
            # Preço
            price = None
            price_element = await element.query_selector("[data-testid='price']")
            if price_element:
                price_text = await price_element.inner_text()
                price = self.extract_price(price_text)
            
            # URL do produto
            product_url = await name_element.get_attribute("href")
            if product_url and not product_url.startswith("http"):
                product_url = f"{self.base_url}{product_url}"
            
            # Imagem
            image_url = None
            img_element = await element.query_selector("[data-testid='product-image']")
            if img_element:
                image_url = await img_element.get_attribute("src")
            
            # Preço original (se houver desconto)
            original_price = None
            original_price_element = await element.query_selector(".price-original, .old-price")
            if original_price_element:
                original_price_text = await original_price_element.inner_text()
                original_price = self.extract_price(original_price_text)
            
            # Desconto percentual
            discount_percentage = None
            if price and original_price and original_price > price:
                discount_percentage = round(((original_price - price) / original_price) * 100, 1)
            
            # Avaliações
            rating = None
            rating_element = await element.query_selector(".v_ratings__stars__enabled")
            if rating_element:
                style = await rating_element.get_attribute("style")
                if style and "width:" in style:
                    width_match = re.search(r"width:\s*(\d+)px", style)
                    if width_match:
                        width = int(width_match.group(1))
                        rating = round((width / 100) * 5, 1)
            
            reviews_count = None
            reviews_element = await element.query_selector(".v_ratings__stars__reviewsCount")
            if reviews_element:
                reviews_text = await reviews_element.inner_text()
                reviews_count = self.extract_number(reviews_text)
            
            # Marca
            brand = None
            if product_name and "EMS" in product_name:
                brand = "EMS"
            elif product_name and "Genérico" in product_name:
                brand = "Genérico"
            
            return {
                "product_name": product_name,
                "price": price,
                "original_price": original_price,
                "discount_percentage": discount_percentage,
                "url": product_url,
                "image_url": image_url,
                "description": None,
                "category": "Farmácia",
                "brand": brand,
                "availability": True,
                "rating": rating,
                "reviews_count": reviews_count,
                "additional_data": {
                    "ean_searched": self.extract_ean_from_url(self.page.url),
                    "scraped_from": self.page.url,
                    "site_type": "farmacia"
                }
            }
        except Exception as e:
            self.logger.error(f"Erro ao extrair dados do produto: {e}")
            return None
    
    def extract_price(self, price_text: str) -> float:
        """Extrai valor numérico do preço"""
        if not price_text:
            return None
        try:
            price_clean = re.sub(r"[^\d,.]", "", price_text)
            price_clean = price_clean.replace(",", ".")
            if price_clean.count(".") > 1:
                parts = price_clean.split(".")
                price_clean = "".join(parts[:-1]) + "." + parts[-1]
            return float(price_clean)
        except:
            return None
    
    def extract_number(self, text: str) -> int:
        """Extrai número de um texto"""
        if not text:
            return None
        try:
            numbers = re.findall(r"\d+", text)
            if numbers:
                return int(numbers[0])
        except:
            return None
    
    def extract_ean_from_url(self, url: str) -> str:
        """Extrai EAN da URL de busca"""
        try:
            parsed_url = urllib.parse.urlparse(url)
            query_params = urllib.parse.parse_qs(parsed_url.query)
            if "w" in query_params:
                return query_params["w"][0]
        except:
            return None

# Database Configuration
DATABASE_URL=postgresql://postgres:password@localhost:5432/pricenow

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000

# Playwright Configuration
PLAYWRIGHT_HEADLESS=true
PLAYWRIGHT_TIMEOUT=60000

# Scraping Configuration
SCRAPING_DELAY=1.0
MAX_RETRIES=3

# Screenshots Configuration
SCREENSHOTS_ENABLED=true
SCREENSHOTS_DIR=screenshots

# Scrapers Habilitados/Desabilitados
# Lista geral de scrapers habilitados (separados por vírgula)
SCRAPERS_ENABLED=example,drogasil

# Configurações específicas por scraper
MERCADOLIVRE_ENABLED=false
AMERICANAS_ENABLED=false
AMAZON_ENABLED=false
DROGASIL_ENABLED=true
EXAMPLE_ENABLED=true

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/scraper.log

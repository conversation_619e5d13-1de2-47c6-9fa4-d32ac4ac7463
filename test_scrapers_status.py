#!/usr/bin/env python3
"""
Script para testar o status dos scrapers (habilitados/desabilitados)
"""

import sys
import os

# Adicionar o diretório raiz ao path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.settings import settings
from config.scrapers import SCRAPER_CONFIGS, get_scraper_config
from main import ScrapingManager
from utils.logger import get_logger

def test_scraper_settings():
    """Testa as configurações de scrapers"""
    logger = get_logger("test_scrapers")
    
    print("=== Status dos Scrapers ===\n")
    
    # Lista de todos os scrapers possíveis
    all_scrapers = ['example', 'mercadolivre', 'drogasil', 'americanas', 'amazon']
    
    print("1. Configurações via settings.py:")
    for scraper in all_scrapers:
        enabled = settings.is_scraper_enabled(scraper)
        status = "✅ HABILITADO" if enabled else "❌ DESABILITADO"
        print(f"   {scraper}: {status}")
    
    print("\n2. Configurações via scrapers.py:")
    for scraper_name, config in SCRAPER_CONFIGS.items():
        enabled = config.is_enabled()
        status = "✅ HABILITADO" if enabled else "❌ DESABILITADO"
        print(f"   {scraper_name}: {status}")
    
    print("\n3. Scrapers registrados no ScrapingManager:")
    manager = ScrapingManager()
    registered_scrapers = manager.get_available_scrapers()
    
    if registered_scrapers:
        print(f"   Scrapers ativos: {', '.join(registered_scrapers)}")
        for scraper in registered_scrapers:
            print(f"   ✅ {scraper}")
    else:
        print("   ❌ Nenhum scraper registrado")
    
    print("\n4. Verificação específica do Mercado Livre:")
    ml_enabled_settings = settings.is_scraper_enabled('mercadolivre')
    ml_config = get_scraper_config('mercadolivre')
    ml_enabled_config = ml_config.is_enabled() if ml_config else False
    ml_registered = 'mercadolivre' in registered_scrapers
    
    print(f"   Settings: {'✅ HABILITADO' if ml_enabled_settings else '❌ DESABILITADO'}")
    print(f"   Config: {'✅ HABILITADO' if ml_enabled_config else '❌ DESABILITADO'}")
    print(f"   Registrado: {'✅ SIM' if ml_registered else '❌ NÃO'}")
    
    # Verificar se está realmente desabilitado
    if not ml_enabled_settings and not ml_enabled_config and not ml_registered:
        print("\n🎉 SUCESSO: Mercado Livre está completamente desabilitado!")
    else:
        print("\n⚠️  ATENÇÃO: Mercado Livre pode ainda estar ativo em algum lugar.")
    
    print("\n5. Configurações de ambiente:")
    print(f"   MERCADOLIVRE_ENABLED: {settings.MERCADOLIVRE_ENABLED}")
    print(f"   SCRAPERS_ENABLED: {settings.SCRAPERS_ENABLED}")
    
    return registered_scrapers

def test_scraper_execution():
    """Testa se é possível executar scrapers habilitados"""
    logger = get_logger("test_execution")
    
    print("\n=== Teste de Execução ===\n")
    
    manager = ScrapingManager()
    available_scrapers = manager.get_available_scrapers()
    
    if not available_scrapers:
        print("❌ Nenhum scraper disponível para teste")
        return
    
    print(f"Scrapers disponíveis para execução: {available_scrapers}")
    
    # Tentar executar um scraper que deveria estar habilitado
    if 'drogasil' in available_scrapers:
        print("✅ Drogasil está disponível (como esperado)")
    else:
        print("⚠️  Drogasil não está disponível")
    
    # Verificar se mercadolivre NÃO está disponível
    if 'mercadolivre' not in available_scrapers:
        print("✅ Mercado Livre não está disponível (como esperado)")
    else:
        print("❌ Mercado Livre ainda está disponível!")

if __name__ == "__main__":
    print("🔍 Testando configurações de scrapers...\n")
    
    try:
        registered_scrapers = test_scraper_settings()
        test_scraper_execution()
        
        print("\n" + "="*50)
        print("📋 RESUMO:")
        print(f"   • Scrapers ativos: {len(registered_scrapers)}")
        print(f"   • Lista: {', '.join(registered_scrapers) if registered_scrapers else 'Nenhum'}")
        print(f"   • Mercado Livre: {'❌ DESABILITADO' if 'mercadolivre' not in registered_scrapers else '⚠️ AINDA ATIVO'}")
        
    except Exception as e:
        print(f"❌ Erro durante o teste: {e}")
        import traceback
        traceback.print_exc()

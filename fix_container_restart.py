#!/usr/bin/env python3
"""
Script para corrigir o problema do container que fica reiniciando
"""

import subprocess
import time
import sys
import os

def run_command(cmd, description="", ignore_errors=False):
    """Executa comando e mostra resultado"""
    print(f"🔧 {description}")
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=30)
        if result.returncode == 0 or ignore_errors:
            print(f"✅ {description} - OK")
            if result.stdout.strip():
                print(f"   Output: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ {description} - ERRO")
            if result.stderr.strip():
                print(f"   Erro: {result.stderr.strip()}")
            return False
    except subprocess.TimeoutExpired:
        print(f"⏰ {description} - TIMEOUT")
        return False
    except Exception as e:
        print(f"❌ {description} - EXCEÇÃO: {e}")
        return False

def stop_all_containers():
    """Para todos os containers do projeto"""
    print("🛑 Parando todos os containers...")
    
    containers = ["pricenow_scraper", "pricenow_api", "pricenow_postgres", "pricenow_redis", "pricenow_pgadmin"]
    
    for container in containers:
        run_command(f"docker stop {container}", f"Parando {container}", ignore_errors=True)
        run_command(f"docker rm {container}", f"Removendo {container}", ignore_errors=True)

def check_docker_status():
    """Verifica status do Docker"""
    print("🐳 Verificando Docker...")
    
    if not run_command("docker --version", "Verificando Docker"):
        print("❌ Docker não está instalado ou não está funcionando")
        return False
    
    if not run_command("docker-compose --version", "Verificando Docker Compose"):
        print("❌ Docker Compose não está instalado")
        return False
    
    return True

def clean_docker_resources():
    """Limpa recursos Docker órfãos"""
    print("🧹 Limpando recursos Docker...")
    
    run_command("docker system prune -f", "Limpando sistema Docker", ignore_errors=True)
    run_command("docker volume prune -f", "Limpando volumes órfãos", ignore_errors=True)
    run_command("docker network prune -f", "Limpando redes órfãs", ignore_errors=True)

def start_without_scraper():
    """Inicia apenas os serviços essenciais (sem scraper)"""
    print("🚀 Iniciando serviços essenciais (sem scraper)...")
    
    # Usar docker-compose.simple.yml que não tem scraper
    if os.path.exists("docker-compose.simple.yml"):
        return run_command("docker-compose -f docker-compose.simple.yml up -d", "Iniciando com docker-compose.simple.yml")
    else:
        # Iniciar serviços individuais
        services = ["postgres", "redis", "api", "pgadmin"]
        success = True
        for service in services:
            if not run_command(f"docker-compose up -d {service}", f"Iniciando {service}"):
                success = False
        return success

def check_services_health():
    """Verifica se os serviços estão saudáveis"""
    print("🏥 Verificando saúde dos serviços...")
    
    # Aguardar um pouco para os serviços iniciarem
    print("⏳ Aguardando serviços iniciarem...")
    time.sleep(10)
    
    # Verificar PostgreSQL
    if run_command("docker exec pricenow_postgres pg_isready -U postgres", "PostgreSQL Health Check"):
        print("✅ PostgreSQL está funcionando")
    else:
        print("❌ PostgreSQL não está respondendo")
    
    # Verificar Redis
    if run_command("docker exec pricenow_redis redis-cli ping", "Redis Health Check"):
        print("✅ Redis está funcionando")
    else:
        print("❌ Redis não está respondendo")
    
    # Verificar API
    time.sleep(5)  # Aguardar API inicializar
    if run_command("curl -f http://localhost:8000/health", "API Health Check"):
        print("✅ API está funcionando")
    else:
        print("❌ API não está respondendo")

def show_container_status():
    """Mostra status dos containers"""
    print("📊 Status dos containers:")
    run_command("docker ps -a --filter name=pricenow", "Listando containers do projeto")

def main():
    """Função principal"""
    print("🔧 CORREÇÃO DO PROBLEMA DE RESTART DO CONTAINER")
    print("=" * 60)
    
    # 1. Verificar Docker
    if not check_docker_status():
        print("❌ Problema com Docker - verifique a instalação")
        return False
    
    # 2. Parar todos os containers
    stop_all_containers()
    
    # 3. Limpar recursos
    clean_docker_resources()
    
    # 4. Aguardar um pouco
    print("⏳ Aguardando limpeza...")
    time.sleep(5)
    
    # 5. Iniciar sem scraper
    if not start_without_scraper():
        print("❌ Falha ao iniciar serviços")
        return False
    
    # 6. Verificar saúde
    check_services_health()
    
    # 7. Mostrar status final
    show_container_status()
    
    print("\n" + "=" * 60)
    print("✅ CORREÇÃO CONCLUÍDA!")
    print()
    print("📋 SERVIÇOS DISPONÍVEIS:")
    print("   • API: http://localhost:8000")
    print("   • Docs: http://localhost:8000/docs")
    print("   • PgAdmin: http://localhost:8080")
    print("   • PostgreSQL: localhost:5432")
    print("   • Redis: localhost:6379")
    print()
    print("🚫 SCRAPER DESABILITADO:")
    print("   • Container do scraper foi removido")
    print("   • Não há mais reinicializações automáticas")
    print("   • Para executar scrapers manualmente:")
    print("     python main.py scrape --scraper drogasil")
    print()
    print("🔧 PARA RECRIAR SCRAPER (se necessário):")
    print("   docker-compose up -d scraper")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n❌ Interrompido pelo usuário")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Erro inesperado: {e}")
        sys.exit(1)

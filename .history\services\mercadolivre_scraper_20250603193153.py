from typing import List, Dict, Any
from core.base_scraper import BaseScraper
import re
import urllib.parse

class MercadoLivreScraper(BaseScraper):
    """Scraper para o Mercado Livre"""
    
    def __init__(self):
        super().__init__("mercadolivre")
        self.base_url = "https://lista.mercadolivre.com.br"
        
        # Termos de busca para diferentes categorias
        self.search_terms = [
            "smartphone",
            "notebook",
            "tablet",
            "fone de ouvido",
            "smartwatch",
            "televisao",
            "geladeira",
            "micro-ondas"
        ]
    
    def get_urls_to_scrape(self) -> List[str]:
        """Retorna URLs de busca do Mercado Livre"""
        urls = []
        
        for term in self.search_terms:
            # Codifica o termo de busca para URL
            encoded_term = urllib.parse.quote(term)
            search_url = f"{self.base_url}/{encoded_term}"
            urls.append(search_url)
        
        return urls
    
    async def scrape(self) -> List[Dict[str, Any]]:
        """Extrai dados da página de resultados do Mercado Livre"""
        products = []
        
        try:
            # Aguarda os resultados carregarem
            await self.page.wait_for_selector('.ui-search-results', timeout=15000)
            
            # Obtém todos os produtos da página
            product_elements = await self.page.query_selector_all('.ui-search-result')
            
            self.logger.info(f"Encontrados {len(product_elements)} produtos na página")
            
            for element in product_elements:
                try:
                    product_data = await self.extract_product_data(element)
                    if product_data:
                        products.append(product_data)
                except Exception as e:
                    self.logger.error(f"Erro ao extrair produto: {e}")
                    continue
            
            self.logger.info(f"Extraídos {len(products)} produtos válidos")
            
        except Exception as e:
            self.logger.error(f"Erro ao fazer scraping da página: {e}")
        
        return products
    
    async def extract_product_data(self, element) -> Dict[str, Any]:
        """Extrai dados de um produto específico do Mercado Livre"""
        try:
            # Nome do produto
            title_element = await element.query_selector('.ui-search-item__title')
            product_name = None
            if title_element:
                product_name = await title_element.inner_text()
                product_name = product_name.strip()
            
            if not product_name:
                return None
            
            # Preço atual
            price_element = await element.query_selector('.andes-money-amount__fraction')
            price = None
            if price_element:
                price_text = await price_element.inner_text()
                price = self.extract_price(price_text)
            
            # Preço original (se houver desconto)
            original_price_element = await element.query_selector('.ui-search-price__second-line .andes-money-amount__fraction')
            original_price = None
            if original_price_element:
                original_price_text = await original_price_element.inner_text()
                original_price = self.extract_price(original_price_text)
            
            # URL do produto
            link_element = await element.query_selector('.ui-search-item__group__element a')
            product_url = None
            if link_element:
                product_url = await link_element.get_attribute('href')
                # Remove parâmetros de tracking
                if product_url and '?' in product_url:
                    product_url = product_url.split('?')[0]
            
            # Imagem
            img_element = await element.query_selector('.ui-search-result-image__element img')
            image_url = None
            if img_element:
                image_url = await img_element.get_attribute('src')
                # Pega a imagem em melhor qualidade se possível
                if image_url and 'I.jpg' in image_url:
                    image_url = image_url.replace('I.jpg', 'O.jpg')
            
            # Frete grátis
            free_shipping_element = await element.query_selector('.ui-search-item__shipping')
            free_shipping = False
            if free_shipping_element:
                shipping_text = await free_shipping_element.inner_text()
                free_shipping = 'grátis' in shipping_text.lower()
            
            # Vendedor
            seller_element = await element.query_selector('.ui-search-item__group__element--seller')
            seller = None
            if seller_element:
                seller = await seller_element.inner_text()
                seller = seller.strip()
            
            # Localização
            location_element = await element.query_selector('.ui-search-item__group__element--location')
            location = None
            if location_element:
                location = await location_element.inner_text()
                location = location.strip()
            
            # Avaliações
            rating_element = await element.query_selector('.ui-search-reviews__rating-number')
            rating = None
            if rating_element:
                rating_text = await rating_element.inner_text()
                rating = self.extract_rating(rating_text)
            
            # Número de avaliações
            reviews_element = await element.query_selector('.ui-search-reviews__amount')
            reviews_count = None
            if reviews_element:
                reviews_text = await reviews_element.inner_text()
                reviews_count = self.extract_number(reviews_text)
            
            # Calcula desconto se houver preço original
            discount_percentage = None
            if price and original_price and original_price > price:
                discount_percentage = round(((original_price - price) / original_price) * 100, 2)
            
            # Categoria (extraída do termo de busca da URL atual)
            category = self.extract_category_from_url(self.page.url)
            
            return {
                'product_name': product_name,
                'price': price,
                'original_price': original_price,
                'discount_percentage': discount_percentage,
                'url': product_url,
                'image_url': image_url,
                'description': None,  # Mercado Livre não mostra descrição na listagem
                'category': category,
                'brand': None,  # Seria necessário entrar na página do produto
                'availability': True,  # Assume disponível se está na listagem
                'rating': rating,
                'reviews_count': reviews_count,
                'additional_data': {
                    'seller': seller,
                    'location': location,
                    'free_shipping': free_shipping,
                    'scraped_from': self.page.url,
                    'search_term': category
                }
            }
            
        except Exception as e:
            self.logger.error(f"Erro ao extrair dados do produto: {e}")
            return None
    
    def extract_price(self, price_text: str) -> float:
        """Extrai valor numérico do preço"""
        if not price_text:
            return None
        
        # Remove espaços e caracteres especiais, mantém apenas números, vírgula e ponto
        price_clean = re.sub(r'[^\d,.]', '', price_text.replace(' ', ''))
        
        # No Mercado Livre, o formato é geralmente "1.234,56"
        if '.' in price_clean and ',' in price_clean:
            # Remove pontos (separadores de milhares) e substitui vírgula por ponto
            price_clean = price_clean.replace('.', '').replace(',', '.')
        elif ',' in price_clean:
            # Se só tem vírgula, substitui por ponto
            price_clean = price_clean.replace(',', '.')
        
        try:
            return float(price_clean)
        except ValueError:
            return None
    
    def extract_rating(self, rating_text: str) -> float:
        """Extrai rating numérico"""
        if not rating_text:
            return None
        
        # Procura por padrão de rating (ex: "4.5", "4,5")
        match = re.search(r'(\d+[.,]\d+|\d+)', rating_text)
        if match:
            rating_str = match.group(1).replace(',', '.')
            try:
                rating = float(rating_str)
                # Garante que o rating está entre 0 e 5
                return min(max(rating, 0), 5)
            except ValueError:
                return None
        return None
    
    def extract_number(self, text: str) -> int:
        """Extrai número inteiro do texto"""
        if not text:
            return None
        
        # Remove caracteres não numéricos
        numbers = re.findall(r'\d+', text)
        if numbers:
            try:
                # Junta todos os números encontrados
                return int(''.join(numbers))
            except ValueError:
                return None
        return None
    
    def extract_category_from_url(self, url: str) -> str:
        """Extrai categoria do termo de busca na URL"""
        try:
            # Procura pelo parâmetro as_word na URL
            if 'as_word=' in url:
                term = url.split('as_word=')[1].split('&')[0]
                # Decodifica URL
                term = urllib.parse.unquote(term)
                return term
            return "Geral"
        except:
            return "Geral"
    
    async def navigate_to_page(self, url: str, wait_for: str = None):
        """Sobrescreve navegação para lidar com possíveis bloqueios"""
        try:
            # Headers específicos para o Mercado Livre
            await self.page.set_extra_http_headers({
                **self.get_headers(),
                'Accept-Language': 'pt-BR,pt;q=0.9,en;q=0.8',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
            })
            
            # Navega para a página
            await self.page.goto(url, timeout=30000, wait_until='networkidle')
            
            # Aguarda um pouco para garantir que a página carregou
            await self.page.wait_for_timeout(2000)
            
            self.logger.info(f"Navegou para: {url}")
            
        except Exception as e:
            self.logger.error(f"Erro ao navegar para {url}: {e}")
            raise

#!/bin/bash
set -e

echo "🚀 Iniciando container do scraper..."

# Verificar modo do scraper
if [ "$SCRAPER_MODE" = "standby" ]; then
    echo "📋 Modo: STANDBY - Scrapers desabilitados"
    echo "🔧 Para executar manualmente: docker exec pricenow_scraper python main.py scrape --scraper <nome>"
    echo "💤 Container ficará em standby..."
    exec "$@"
fi

# Iniciar Xvfb para modo headless se necessário
if [ "$PLAYWRIGHT_HEADLESS" != "true" ]; then
    echo "🖥️  Iniciando Xvfb para modo não-headless..."
    Xvfb :99 -screen 0 1920x1080x24 &
    export DISPLAY=:99
fi

# Aguardar banco de dados estar disponível (com timeout)
echo "🗄️  Aguardando banco de dados..."
timeout=60
counter=0
while ! nc -z postgres 5432; do
    sleep 1
    counter=$((counter + 1))
    if [ $counter -ge $timeout ]; then
        echo "❌ Timeout aguardando banco de dados após ${timeout}s"
        exit 1
    fi
done
echo "✅ Banco de dados disponível!"

# Inicializar banco se necessário (com tratamento de erro)
echo "🔧 Verificando inicialização do banco..."
if python main.py init; then
    echo "✅ Banco inicializado com sucesso"
else
    echo "⚠️  Erro na inicialização do banco ou já inicializado"
fi

# Executar comando passado
echo "▶️  Executando: $@"
exec "$@"

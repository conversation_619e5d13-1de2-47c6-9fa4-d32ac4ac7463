# ⚡ Solução Rápida - <PERSON>rro de Dependências

## 🚨 Problema
```
ERROR: Cannot install -r requirements.txt (line 2), -r requirements.txt (line 8) and pydantic==1.10.12 because these package versions have conflicting dependencies.
```

## ✅ Solução Imediata

### 🐳 Opção 1: Docker (Recomendado - 100% Funciona)
```bash
# 1. Clone o projeto
git clone <repository-url>
cd pricenow

# 2. Inicie com Docker (resolve todos os conflitos)
cp .env.example .env
docker-compose up -d

# 3. Aguarde 2-3 minutos e acesse:
# http://localhost:8000/docs
```

**Pronto! Zero problemas de dependências.**

---

### 💻 Opção 2: Instalação Local (Corrigida)

#### Passo 1: Ambiente <PERSON>
```bash
# Remover ambiente antigo
rm -rf venv  # Linux/Mac
rmdir /s venv  # Windows

# Criar novo ambiente
python -m venv venv
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate     # Windows

# Atualizar pip
pip install --upgrade pip
```

#### Passo 2: Instalar Dependências (Ordem Correta)
```bash
# Core primeiro (versões compatíveis)
pip install fastapi==0.104.1
pip install pydantic==2.5.0
pip install pydantic-settings==2.0.3
pip install uvicorn[standard]==0.24.0

# Resto das dependências
pip install sqlalchemy==2.0.23
pip install psycopg2-binary==2.9.9
pip install playwright==1.40.0
pip install python-dotenv==1.0.0
pip install loguru==0.7.2
pip install asyncpg==0.29.0
pip install alembic==1.12.1
pip install beautifulsoup4==4.12.2
pip install httpx==0.25.2
pip install schedule==1.2.0
pip install pandas==2.1.3

# Instalar browsers
playwright install
```

#### Passo 3: Testar
```bash
# Testar dependências
python test_dependencies.py

# Se tudo OK, inicializar
cp .env.example .env
docker-compose -f docker-compose.dev.yml up -d  # Apenas banco
python main.py init
python main.py api
```

---

## 🔍 Verificação Rápida

### Teste se funcionou:
```bash
# Verificar versões
python -c "import fastapi, pydantic; print(f'FastAPI: {fastapi.__version__}, Pydantic: {pydantic.__version__}')"

# Resultado esperado:
# FastAPI: 0.104.1, Pydantic: 2.5.0
```

### Se ainda houver erro:
```bash
# Usar versões mais conservadoras
pip install fastapi==0.100.0 pydantic==2.4.0 uvicorn==0.23.0
```

---

## 🎯 Por que o Docker é Melhor?

### ✅ Vantagens:
- **Zero conflitos** de dependências
- **Ambiente isolado** e controlado
- **Tudo pré-configurado**: Python, Playwright, browsers, banco
- **Funciona em qualquer sistema**
- **Apenas 3 comandos** para rodar

### 🚀 Execução Docker:
```bash
git clone <repository-url>
cd pricenow
docker-compose up -d
# Pronto! http://localhost:8000
```

---

## 📞 Ainda com Problemas?

### 1. **Use Docker** (elimina 99% dos problemas)
```bash
docker-compose up -d
```

### 2. **Verifique Python**
```bash
python --version  # Deve ser 3.8+
```

### 3. **Limpe cache pip**
```bash
pip cache purge
```

### 4. **Instale uma por vez**
```bash
pip install fastapi==0.104.1
pip install pydantic==2.5.0
# ... continue se funcionou
```

---

## 🎉 Resultado Final

### Com Docker:
- ✅ API: http://localhost:8000
- ✅ Docs: http://localhost:8000/docs
- ✅ PgAdmin: http://localhost:8080
- ✅ Scrapers: `docker exec pricenow_scraper python main.py scrape --all`

### Com instalação local:
- ✅ API: `python main.py api`
- ✅ Scrapers: `python main.py scrape --all`

**O problema foi corrigido! As dependências agora são compatíveis.** 🚀

import re
from typing import List, Dict, Any
from core.base_scraper import Base<PERSON><PERSON>raper
from playwright.async_api import TimeoutError as PlaywrightTimeoutError

class DrogasilScraper(BaseScraper):
    """Scraper para o site Drogasil - busca por EAN"""

    def __init__(self, eans: List[str] = None):
        super().__init__("drogasil")
        self.base_url = "https://www.drogasil.com.br"
        self.eans = eans or ["7896004703398"]
    
    def get_urls_to_scrape(self) -> List[str]:
        return [f"{self.base_url}/search?w={ean}" for ean in self.eans]

    async def setup_browser(self):
        """Configuração básica do browser"""
        await super().setup_browser()

        # User agent realista
        user_agent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'

        # Headers básicos
        await self.page.set_extra_http_headers({
            'User-Agent': user_agent,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
            'Accept-Language': 'pt-BR,pt;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br'
        })

    async def navigate_to_drogasil_page(self, url: str):
        """Navegação específica para Drogasil com tratamento de timeout"""
        try:
            self.logger.info(f"Navegando para: {url}")
            await self.page.goto(url, timeout=60000, wait_until="domcontentloaded")

            # Aguardar um pouco para a página carregar completamente
            await self.page.wait_for_timeout(3000)

            # Verificar se a página carregou corretamente
            page_title = await self.page.title()
            self.logger.info(f"Página carregada: {page_title}")

        except PlaywrightTimeoutError as e:
            self.logger.error(f"Timeout ao navegar para {url}: {e}")
            await self.take_error_screenshot("navigation_timeout")
            raise
        except Exception as e:
            self.logger.error(f"Erro ao navegar para {url}: {e}")
            await self.take_error_screenshot("navigation_error")
            raise

    async def scrape(self) -> List[Dict[str, Any]]:
        """Extrai dados da página de resultados do Drogasil"""
        products = []

        try:
            # Aguardar produtos carregarem com timeout maior
            self.logger.info("Aguardando produtos carregarem...")
            await self.page.wait_for_selector("article[data-item-id]", timeout=60000)
            product_elements = await self.page.query_selector_all("article[data-item-id]")
            self.logger.info(f"Encontrados {len(product_elements)} produtos na página")

            for element in product_elements:
                product_data = await self.extract_product_data(element)
                if product_data:
                    products.append(product_data)
                    self.logger.info(f"Produto extraído: {product_data['product_name']}")

        except PlaywrightTimeoutError as e:
            self.logger.error(f"Timeout ao aguardar produtos carregarem: {e}")
            # Capturar screenshot do timeout
            screenshot_path = await self.take_error_screenshot("timeout_produtos")
            self.logger.error(f"Screenshot do timeout salvo em: {screenshot_path}")

        except Exception as e:
            self.logger.error(f"Erro ao fazer scraping da página: {e}")
            await self.take_error_screenshot("scraping_error")

        return products

    async def extract_product_data(self, element) -> Dict[str, Any]:
        """Extrai dados de um produto específico do Drogasil"""
        try:
            # Nome do produto
            name_element = await element.query_selector("h2 a")
            if not name_element:
                return None
            product_name = (await name_element.inner_text()).strip()

            # URL do produto
            product_url = await name_element.get_attribute("href")
            if product_url and not product_url.startswith("http"):
                product_url = f"{self.base_url}{product_url}"

            # Preço
            price = None
            price_element = await element.query_selector("[data-testid='price']")
            if price_element:
                price_text = await price_element.inner_text()
                price = self.extract_price(price_text)

            # Imagem
            image_url = None
            img_element = await element.query_selector("[data-testid='product-image']")
            if img_element:
                image_url = await img_element.get_attribute("src")

            # Marca (extrair do nome do produto)
            brand = None
            if product_name:
                if "EMS" in product_name:
                    brand = "EMS"
                elif "Genérico" in product_name:
                    brand = "Genérico"

            return {
                "product_name": product_name,
                "price": price,
                "url": product_url,
                "image_url": image_url,
                "category": "Farmácia",
                "brand": brand,
                "availability": True,
                "additional_data": {
                    "scraped_from": self.page.url,
                    "site_type": "farmacia"
                }
            }
        except Exception as e:
            self.logger.error(f"Erro ao extrair dados do produto: {e}")
            return None
    
    def extract_price(self, price_text: str) -> float:
        """Extrai valor numérico do preço"""
        if not price_text:
            return None
        try:
            price_clean = re.sub(r"[^\d,.]", "", price_text)
            price_clean = price_clean.replace(",", ".")
            if price_clean.count(".") > 1:
                parts = price_clean.split(".")
                price_clean = "".join(parts[:-1]) + "." + parts[-1]
            return float(price_clean)
        except:
            return None

    async def run_scraping(self):
        """Executa o processo completo de scraping com navegação específica para Drogasil"""
        job_id = self.create_scraping_job()
        items_scraped = 0
        errors_count = 0

        try:
            async with self:
                urls = self.get_urls_to_scrape()
                self.logger.info(f"Iniciando scraping de {len(urls)} URLs da Drogasil")

                for url in urls:
                    try:
                        # Usar navegação específica para Drogasil
                        await self.navigate_to_drogasil_page(url)
                        data_list = await self.scrape()

                        for data in data_list:
                            self.save_scraped_data(data)
                            items_scraped += 1

                    except Exception as e:
                        errors_count += 1
                        self.logger.error(f"Erro ao processar {url}: {e}")
                        # Capturar screenshot do erro
                        await self.take_error_screenshot(f"url_error_{errors_count}")

                self.update_scraping_job("completed", items_scraped, errors_count)
                self.logger.info(f"Scraping da Drogasil concluído: {items_scraped} itens, {errors_count} erros")

        except Exception as e:
            self.update_scraping_job("failed", items_scraped, errors_count, str(e))
            self.logger.error(f"Falha no scraping da Drogasil: {e}")
            await self.take_error_screenshot("scraping_failed")
            raise

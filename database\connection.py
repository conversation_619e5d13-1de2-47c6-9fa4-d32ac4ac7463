from sqlalchemy import create_engine
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker, Session
from config.settings import settings
from database.models import Base
import asyncio

# Sync engine para operações síncronas
sync_engine = create_engine(settings.DATABASE_URL)

# Async engine para operações assíncronas
async_database_url = settings.DATABASE_URL.replace("postgresql://", "postgresql+asyncpg://")
async_engine = create_async_engine(async_database_url)

# Session makers
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=sync_engine)
AsyncSessionLocal = sessionmaker(
    async_engine, class_=AsyncSession, expire_on_commit=False
)

def create_tables():
    """Cria todas as tabelas no banco de dados"""
    Base.metadata.create_all(bind=sync_engine)

async def create_tables_async():
    """Cria todas as tabelas no banco de dados (versão async)"""
    async with async_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

def get_db() -> Session:
    """Dependency para obter sessão do banco de dados (sync)"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

async def get_async_db() -> AsyncSession:
    """Dependency para obter sessão do banco de dados (async)"""
    async with AsyncSessionLocal() as session:
        yield session

class DatabaseManager:
    """Gerenciador de conexões com o banco de dados"""
    
    @staticmethod
    def get_sync_session() -> Session:
        """Retorna uma sessão síncrona"""
        return SessionLocal()
    
    @staticmethod
    async def get_async_session() -> AsyncSession:
        """Retorna uma sessão assíncrona"""
        return AsyncSessionLocal()
    
    @staticmethod
    def init_db():
        """Inicializa o banco de dados"""
        create_tables()
    
    @staticmethod
    async def init_db_async():
        """Inicializa o banco de dados (versão async)"""
        await create_tables_async()

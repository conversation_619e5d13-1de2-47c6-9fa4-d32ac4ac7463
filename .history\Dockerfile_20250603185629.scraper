# Dockerfile específico para scrapers (com interface gráfica se necessário)
FROM python:3.11-slim

# Definir variáveis de ambiente
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PLAYWRIGHT_BROWSERS_PATH=/ms-playwright \
    DISPLAY=:99

# Instalar dependências do sistema incluindo Xvfb para modo headless
RUN apt-get update && apt-get install -y \
    wget \
    gnupg \
    ca-certificates \
    fonts-liberation \
    libasound2 \
    libatk-bridge2.0-0 \
    libatk1.0-0 \
    libatspi2.0-0 \
    libcups2 \
    libdbus-1-3 \
    libdrm2 \
    libgtk-3-0 \
    libnspr4 \
    libnss3 \
    libwayland-client0 \
    libx11-6 \
    libx11-xcb1 \
    libxcb1 \
    libxcomposite1 \
    libxdamage1 \
    libxext6 \
    libxfixes3 \
    libxrandr2 \
    libxss1 \
    libxtst6 \
    lsb-release \
    xdg-utils \
    libu2f-udev \
    libvulkan1 \
    xvfb \
    netcat-openbsd \
    && rm -rf /var/lib/apt/lists/*

# Criar usuário não-root
RUN groupadd -r pricenow && useradd -r -g pricenow pricenow

# Criar diretório da aplicação
WORKDIR /app

# Copiar requirements primeiro para aproveitar cache do Docker
COPY requirements.txt .

# Instalar dependências Python
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Instalar Playwright e browsers
RUN playwright install chromium firefox webkit && \
    playwright install-deps

# Copiar código da aplicação
COPY . .

# Criar diretórios necessários
RUN mkdir -p logs && \
    chown -R pricenow:pricenow /app

# Script de inicialização
COPY docker-entrypoint-scraper.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/docker-entrypoint-scraper.sh

# Mudar para usuário não-root
USER pricenow

# Comando padrão
ENTRYPOINT ["/usr/local/bin/docker-entrypoint-scraper.sh"]
CMD ["python", "main.py", "scrape", "--all"]

# Makefile para facilitar comandos Docker
.PHONY: help build up down logs clean dev prod backup restore

# Variáveis
COMPOSE_FILE = docker-compose.yml
DEV_COMPOSE_FILE = docker-compose.dev.yml
PROD_COMPOSE_FILE = docker-compose.prod.yml

help: ## Mostra esta ajuda
	@echo "Comandos disponíveis:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

build: ## Constrói as imagens Docker
	docker-compose -f $(COMPOSE_FILE) build

build-dev: ## Constrói imagens para desenvolvimento
	docker-compose -f $(DEV_COMPOSE_FILE) build

build-prod: ## Constrói imagens para produção
	docker-compose -f $(PROD_COMPOSE_FILE) build

up: ## Inicia todos os serviços
	docker-compose -f $(COMPOSE_FILE) up -d

up-dev: ## Inicia serviços de desenvolvimento (apenas banco)
	docker-compose -f $(DEV_COMPOSE_FILE) up -d

up-prod: ## Inicia serviços de produção
	docker-compose -f $(PROD_COMPOSE_FILE) up -d

down: ## Para todos os serviços
	docker-compose -f $(COMPOSE_FILE) down

down-dev: ## Para serviços de desenvolvimento
	docker-compose -f $(DEV_COMPOSE_FILE) down

down-prod: ## Para serviços de produção
	docker-compose -f $(PROD_COMPOSE_FILE) down

restart: ## Reinicia todos os serviços
	docker-compose -f $(COMPOSE_FILE) restart

logs: ## Mostra logs de todos os serviços
	docker-compose -f $(COMPOSE_FILE) logs -f

logs-api: ## Mostra logs da API
	docker-compose -f $(COMPOSE_FILE) logs -f api

logs-scraper: ## Mostra logs do scraper
	docker-compose -f $(COMPOSE_FILE) logs -f scraper

logs-postgres: ## Mostra logs do PostgreSQL
	docker-compose -f $(COMPOSE_FILE) logs -f postgres

status: ## Mostra status dos containers
	docker-compose -f $(COMPOSE_FILE) ps

clean: ## Remove containers, volumes e imagens não utilizados
	docker-compose -f $(COMPOSE_FILE) down -v
	docker system prune -f
	docker volume prune -f

clean-all: ## Remove tudo (CUIDADO: apaga dados!)
	docker-compose -f $(COMPOSE_FILE) down -v --rmi all
	docker-compose -f $(DEV_COMPOSE_FILE) down -v --rmi all
	docker-compose -f $(PROD_COMPOSE_FILE) down -v --rmi all
	docker system prune -af
	docker volume prune -f

shell-api: ## Acessa shell do container da API
	docker exec -it pricenow_api bash

shell-scraper: ## Acessa shell do container do scraper
	docker exec -it pricenow_scraper bash

shell-postgres: ## Acessa shell do PostgreSQL
	docker exec -it pricenow_postgres psql -U postgres -d pricenow

init-db: ## Inicializa o banco de dados
	docker exec pricenow_api python main.py init

run-scraper: ## Executa scraper manualmente
	docker exec pricenow_scraper python main.py scrape --all

run-scraper-ml: ## Executa scraper do Mercado Livre
	docker exec pricenow_scraper python main.py scrape --scraper mercadolivre

run-scraper-americanas: ## Executa scraper das Americanas
	docker exec pricenow_scraper python main.py scrape --scraper americanas

backup: ## Faz backup do banco de dados
	@echo "Fazendo backup do banco de dados..."
	mkdir -p ./backups
	docker exec pricenow_postgres pg_dump -U postgres pricenow > ./backups/backup_$(shell date +%Y%m%d_%H%M%S).sql
	@echo "Backup salvo em ./backups/"

restore: ## Restaura backup do banco (uso: make restore FILE=backup.sql)
	@if [ -z "$(FILE)" ]; then echo "Uso: make restore FILE=backup.sql"; exit 1; fi
	@echo "Restaurando backup $(FILE)..."
	docker exec -i pricenow_postgres psql -U postgres -d pricenow < ./backups/$(FILE)
	@echo "Backup restaurado!"

dev-setup: ## Configuração completa para desenvolvimento
	@echo "Configurando ambiente de desenvolvimento..."
	cp .env.example .env
	make up-dev
	@echo "Aguardando banco de dados..."
	sleep 10
	@echo "Ambiente de desenvolvimento pronto!"
	@echo "Banco PostgreSQL: localhost:5432"
	@echo "PgAdmin: http://localhost:8080"

prod-setup: ## Configuração completa para produção
	@echo "Configurando ambiente de produção..."
	@if [ ! -f .env ]; then echo "Configure o arquivo .env antes de continuar!"; exit 1; fi
	make build-prod
	make up-prod
	@echo "Aguardando serviços..."
	sleep 30
	make init-db
	@echo "Ambiente de produção pronto!"
	@echo "API: http://localhost:8000"
	@echo "Documentação: http://localhost:8000/docs"

monitor: ## Monitora recursos dos containers
	docker stats pricenow_api pricenow_scraper pricenow_postgres

update: ## Atualiza e reconstrói containers
	git pull
	make build
	make down
	make up
	@echo "Aplicação atualizada!"

# Comandos de desenvolvimento local (sem Docker)
install: ## Instala dependências localmente
	pip install -r requirements.txt
	playwright install

run-local: ## Executa API localmente
	python main.py api

scrape-local: ## Executa scraper localmente
	python main.py scrape --all

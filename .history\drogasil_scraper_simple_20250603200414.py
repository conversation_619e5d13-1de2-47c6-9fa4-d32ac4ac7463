import re
import urllib.parse
from typing import List, Dict, Any
from core.base_scraper import BaseScraper

class DrogasilScraper(BaseScraper):
    def __init__(self, eans: List[str] = None):
        super().__init__("drogasil")
        self.base_url = "https://www.drogasil.com.br"
        self.eans = eans or ["7896004703398"]

    async def setup_browser(self):
        """Configuração específica do browser para evitar detecção"""
        await super().setup_browser()

        # Configurações anti-detecção
        await self.page.add_init_script("""
            // Remove webdriver property
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });

            // Mock plugins
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });

            // Mock languages
            Object.defineProperty(navigator, 'languages', {
                get: () => ['pt-BR', 'pt', 'en-US', 'en'],
            });

            // Mock chrome object
            window.chrome = {
                runtime: {},
            };

            // Mock permissions
            const originalQuery = window.navigator.permissions.query;
            window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                    Promise.resolve({ state: Cypress.env('NOTIFICATION_PERMISSION') || 'granted' }) :
                    originalQuery(parameters)
            );
        """)

        # Headers mais realistas
        await self.page.set_extra_http_headers({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"'
        })
    
    def get_urls_to_scrape(self) -> List[str]:
        return [f"{self.base_url}/search?w={ean}" for ean in self.eans]
    
    async def scrape(self) -> List[Dict[str, Any]]:
        products = []
        try:
            await self.page.wait_for_selector("article[data-item-id]", timeout=15000)
            product_elements = await self.page.query_selector_all("article[data-item-id]")
            self.logger.info(f"Encontrados {len(product_elements)} produtos na página")
            
            for element in product_elements:
                product_data = await self.extract_product_data(element)
                if product_data:
                    products.append(product_data)
                    self.logger.info(f"Produto extraído: {product_data['product_name']}")
        except Exception as e:
            self.logger.error(f"Erro ao fazer scraping da página: {e}")
        return products
    
    async def extract_product_data(self, element) -> Dict[str, Any]:
        try:
            # Nome do produto
            name_element = await element.query_selector("h2 a")
            if not name_element:
                return None
            product_name = (await name_element.inner_text()).strip()
            
            # Preço
            price = None
            price_element = await element.query_selector("[data-testid='price']")
            if price_element:
                price_text = await price_element.inner_text()
                price = self.extract_price(price_text)
            
            # URL do produto
            product_url = await name_element.get_attribute("href")
            if product_url and not product_url.startswith("http"):
                product_url = f"{self.base_url}{product_url}"
            
            # Imagem
            image_url = None
            img_element = await element.query_selector("[data-testid='product-image']")
            if img_element:
                image_url = await img_element.get_attribute("src")
            
            return {
                "product_name": product_name,
                "price": price,
                "url": product_url,
                "image_url": image_url,
                "category": "Farmácia",
                "availability": True,
                "additional_data": {
                    "scraped_from": self.page.url,
                    "site_type": "farmacia"
                }
            }
        except Exception as e:
            self.logger.error(f"Erro ao extrair dados do produto: {e}")
            return None
    
    def extract_price(self, price_text: str) -> float:
        if not price_text:
            return None
        try:
            price_clean = re.sub(r"[^\d,.]", "", price_text)
            price_clean = price_clean.replace(",", ".")
            if price_clean.count(".") > 1:
                parts = price_clean.split(".")
                price_clean = "".join(parts[:-1]) + "." + parts[-1]
            return float(price_clean)
        except:
            return None

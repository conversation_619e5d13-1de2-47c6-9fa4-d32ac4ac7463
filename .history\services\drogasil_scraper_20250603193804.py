from typing import List, Dict, Any
from core.base_scraper import BaseScraper
import re
import urllib.parse

class DrogasilScraper(BaseScraper):
    """Scraper para o site Drogasil - busca por EAN"""
    
    def __init__(self, eans: List[str] = None):
        super().__init__("drogasil")
        self.base_url = "https://www.drogasil.com.br"
        
        # Lista de EANs para buscar (pode ser passada no construtor ou usar padrão)
        self.eans = eans or [
            "7891000100103",  # Exemplo: Coca-Cola
            "7891000053508",  # Exemplo: Fanta
            "7891000244234",  # Exemplo: Sprite
            "7891991010016",  # Exemplo: Paracetamol
            "7891106001533",  # Exemplo: Dipirona
            "7891106001540",  # Exemplo: Ibuprofeno
            "7891106001557",  # Exemplo: <PERSON><PERSON><PERSON>
            "7891106001564",  # Exemplo: Vitamina C
        ]
    
    def get_urls_to_scrape(self) -> List[str]:
        """Retorna URLs de busca por EAN do Drogasil"""
        urls = []
        
        for ean in self.eans:
            # URL de busca por EAN
            search_url = f"{self.base_url}/search?w={ean}"
            urls.append(search_url)
        
        return urls
    
    async def scrape(self) -> List[Dict[str, Any]]:
        """Extrai dados da página de resultados do Drogasil"""
        products = []
        
        try:
            # Aguarda os resultados carregarem
            await self.page.wait_for_selector('.showcase-item, .product-item, .item-product', timeout=15000)
            
            # Obtém todos os produtos da página
            product_elements = await self.page.query_selector_all('.showcase-item, .product-item, .item-product, [data-testid="product-card"]')
            
            self.logger.info(f"Encontrados {len(product_elements)} produtos na página")
            
            for element in product_elements:
                try:
                    product_data = await self.extract_product_data(element)
                    if product_data:
                        products.append(product_data)
                except Exception as e:
                    self.logger.error(f"Erro ao extrair produto: {e}")
                    continue
            
            # Se não encontrou produtos com os seletores principais, tenta seletores alternativos
            if not products:
                alternative_selectors = [
                    '.product-card',
                    '.product-tile',
                    '.product-box',
                    '.item',
                    '[class*="product"]',
                    '[class*="item"]'
                ]
                
                for selector in alternative_selectors:
                    elements = await self.page.query_selector_all(selector)
                    if elements:
                        self.logger.info(f"Tentando seletor alternativo: {selector} ({len(elements)} elementos)")
                        for element in elements:
                            try:
                                product_data = await self.extract_product_data(element)
                                if product_data:
                                    products.append(product_data)
                            except Exception as e:
                                continue
                        if products:
                            break
            
            self.logger.info(f"Extraídos {len(products)} produtos válidos")
            
        except Exception as e:
            self.logger.error(f"Erro ao fazer scraping da página: {e}")
        
        return products
    
    async def extract_product_data(self, element) -> Dict[str, Any]:
        """Extrai dados de um produto específico do Drogasil"""
        try:
            # Nome do produto - tenta vários seletores
            product_name = None
            name_selectors = [
                '.product-name',
                '.product-title',
                '.item-name',
                '.title',
                'h2',
                'h3',
                '[class*="name"]',
                '[class*="title"]'
            ]
            
            for selector in name_selectors:
                name_element = await element.query_selector(selector)
                if name_element:
                    product_name = await name_element.inner_text()
                    product_name = product_name.strip()
                    if product_name:
                        break
            
            if not product_name:
                return None
            
            # Preço atual - tenta vários seletores
            price = None
            price_selectors = [
                '.price-current',
                '.price-value',
                '.current-price',
                '.price',
                '[class*="price"]',
                '.valor',
                '.preco'
            ]
            
            for selector in price_selectors:
                price_element = await element.query_selector(selector)
                if price_element:
                    price_text = await price_element.inner_text()
                    price = self.extract_price(price_text)
                    if price:
                        break
            
            # Preço original (se houver desconto)
            original_price = None
            original_price_selectors = [
                '.price-original',
                '.price-old',
                '.old-price',
                '.price-from',
                '[class*="original"]',
                '[class*="old"]'
            ]
            
            for selector in original_price_selectors:
                original_price_element = await element.query_selector(selector)
                if original_price_element:
                    original_price_text = await original_price_element.inner_text()
                    original_price = self.extract_price(original_price_text)
                    if original_price:
                        break
            
            # URL do produto
            product_url = None
            link_element = await element.query_selector('a')
            if link_element:
                product_url = await link_element.get_attribute('href')
                if product_url and not product_url.startswith('http'):
                    product_url = f"{self.base_url}{product_url}"
                # Remove parâmetros de tracking
                if product_url and '?' in product_url:
                    product_url = product_url.split('?')[0]
            
            # Imagem
            image_url = None
            img_element = await element.query_selector('img')
            if img_element:
                image_url = await img_element.get_attribute('src') or await img_element.get_attribute('data-src')
                if image_url and not image_url.startswith('http'):
                    if image_url.startswith('//'):
                        image_url = f"https:{image_url}"
                    else:
                        image_url = f"{self.base_url}{image_url}"
            
            # Desconto percentual
            discount_percentage = None
            discount_selectors = [
                '.discount',
                '.desconto',
                '.off',
                '[class*="discount"]',
                '[class*="desconto"]'
            ]
            
            for selector in discount_selectors:
                discount_element = await element.query_selector(selector)
                if discount_element:
                    discount_text = await discount_element.inner_text()
                    discount_percentage = self.extract_discount_percentage(discount_text)
                    if discount_percentage:
                        break
            
            # Se não encontrou desconto mas tem preços, calcula
            if not discount_percentage and price and original_price and original_price > price:
                discount_percentage = round(((original_price - price) / original_price) * 100, 2)
            
            # Disponibilidade
            availability = True
            unavailable_selectors = [
                '.unavailable',
                '.out-of-stock',
                '.indisponivel',
                '[class*="unavailable"]',
                '[class*="indisponivel"]'
            ]
            
            for selector in unavailable_selectors:
                unavailable_element = await element.query_selector(selector)
                if unavailable_element:
                    availability = False
                    break
            
            # Marca/Laboratório
            brand = None
            brand_selectors = [
                '.brand',
                '.marca',
                '.laboratory',
                '.laboratorio',
                '[class*="brand"]',
                '[class*="marca"]'
            ]
            
            for selector in brand_selectors:
                brand_element = await element.query_selector(selector)
                if brand_element:
                    brand = await brand_element.inner_text()
                    brand = brand.strip()
                    if brand:
                        break
            
            # EAN da busca atual (extraído da URL)
            current_ean = self.extract_ean_from_url(self.page.url)
            
            return {
                'product_name': product_name,
                'price': price,
                'original_price': original_price,
                'discount_percentage': discount_percentage,
                'url': product_url,
                'image_url': image_url,
                'description': None,  # Drogasil não mostra descrição na listagem
                'category': 'Farmácia',  # Categoria padrão para Drogasil
                'brand': brand,
                'availability': availability,
                'rating': None,  # Drogasil geralmente não mostra rating na listagem
                'reviews_count': None,
                'additional_data': {
                    'ean_searched': current_ean,
                    'scraped_from': self.page.url,
                    'site_type': 'farmacia'
                }
            }
            
        except Exception as e:
            self.logger.error(f"Erro ao extrair dados do produto: {e}")
            return None
    
    def extract_price(self, price_text: str) -> float:
        """Extrai valor numérico do preço"""
        if not price_text:
            return None
        
        # Remove caracteres especiais, mantém apenas números, vírgula e ponto
        price_clean = re.sub(r'[^\d,.]', '', price_text.replace(' ', ''))
        
        # No Drogasil, o formato é geralmente "R$ 12,34"
        if '.' in price_clean and ',' in price_clean:
            # Remove pontos (separadores de milhares) e substitui vírgula por ponto
            price_clean = price_clean.replace('.', '').replace(',', '.')
        elif ',' in price_clean:
            # Se só tem vírgula, substitui por ponto
            price_clean = price_clean.replace(',', '.')
        
        try:
            return float(price_clean)
        except ValueError:
            return None
    
    def extract_discount_percentage(self, discount_text: str) -> float:
        """Extrai percentual de desconto"""
        if not discount_text:
            return None
        
        # Procura por padrão de desconto (ex: "20%", "20% OFF")
        match = re.search(r'(\d+)%', discount_text)
        if match:
            try:
                return float(match.group(1))
            except ValueError:
                return None
        return None
    
    def extract_ean_from_url(self, url: str) -> str:
        """Extrai EAN da URL de busca"""
        try:
            if 'w=' in url:
                ean = url.split('w=')[1].split('&')[0]
                # Decodifica URL
                ean = urllib.parse.unquote(ean)
                return ean
            return None
        except:
            return None
    
    async def navigate_to_page(self, url: str, wait_for: str = None):
        """Sobrescreve navegação para lidar com especificidades do Drogasil"""
        try:
            # Headers específicos para o Drogasil
            await self.page.set_extra_http_headers({
                **self.get_headers(),
                'Accept-Language': 'pt-BR,pt;q=0.9,en;q=0.8',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none'
            })
            
            # Navega para a página
            await self.page.goto(url, timeout=30000, wait_until='networkidle')
            
            # Aguarda um pouco para garantir que a página carregou
            await self.page.wait_for_timeout(3000)
            
            # Tenta fechar possíveis modais ou popups
            try:
                close_selectors = [
                    '[data-testid="modal-close"]',
                    '.modal-close',
                    '.close-modal',
                    '.popup-close',
                    '[class*="close"]'
                ]
                
                for selector in close_selectors:
                    close_modal = await self.page.query_selector(selector)
                    if close_modal:
                        await close_modal.click()
                        await self.page.wait_for_timeout(1000)
                        break
            except:
                pass
            
            # Aceita cookies se necessário
            try:
                cookie_selectors = [
                    '[data-testid="cookie-accept"]',
                    '.cookie-accept',
                    '.accept-cookies',
                    '#accept-cookies'
                ]
                
                for selector in cookie_selectors:
                    cookie_button = await self.page.query_selector(selector)
                    if cookie_button:
                        await cookie_button.click()
                        await self.page.wait_for_timeout(1000)
                        break
            except:
                pass
            
            self.logger.info(f"Navegou para: {url}")
            
        except Exception as e:
            self.logger.error(f"Erro ao navegar para {url}: {e}")
            raise
    
    def set_eans(self, eans: List[str]):
        """Define lista de EANs para buscar"""
        self.eans = eans
        self.logger.info(f"EANs configurados: {len(eans)} códigos")
    
    def add_ean(self, ean: str):
        """Adiciona um EAN à lista de busca"""
        if ean not in self.eans:
            self.eans.append(ean)
            self.logger.info(f"EAN adicionado: {ean}")
    
    def remove_ean(self, ean: str):
        """Remove um EAN da lista de busca"""
        if ean in self.eans:
            self.eans.remove(ean)
            self.logger.info(f"EAN removido: {ean}")
    
    def get_eans(self) -> List[str]:
        """Retorna lista atual de EANs"""
        return self.eans.copy()

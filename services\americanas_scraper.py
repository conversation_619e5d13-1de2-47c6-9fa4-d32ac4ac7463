from typing import List, Dict, Any
from core.base_scraper import BaseScraper
import re
import urllib.parse

class AmericanasScraper(BaseScraper):
    """Scraper para o site Americanas"""
    
    def __init__(self):
        super().__init__("americanas")
        self.base_url = "https://www.americanas.com.br"
        
        # Categorias para fazer scraping
        self.categories = [
            "celulares-e-smartphones",
            "notebooks",
            "tablets",
            "fones-de-ouvido",
            "smartwatch-e-relogios-inteligentes",
            "tv-e-home-theater",
            "refrigeradores",
            "micro-ondas",
            "games",
            "livros"
        ]
    
    def get_urls_to_scrape(self) -> List[str]:
        """Retorna URLs de categorias das Americanas"""
        urls = []
        
        for category in self.categories:
            category_url = f"{self.base_url}/categoria/{category}"
            urls.append(category_url)
        
        return urls
    
    async def scrape(self) -> List[Dict[str, Any]]:
        """Extrai dados da página de categoria das Americanas"""
        products = []
        
        try:
            # Aguarda os produtos carregarem
            await self.page.wait_for_selector('[data-testid="product-card"]', timeout=15000)
            
            # Obtém todos os produtos da página
            product_elements = await self.page.query_selector_all('[data-testid="product-card"]')
            
            self.logger.info(f"Encontrados {len(product_elements)} produtos na página")
            
            for element in product_elements:
                try:
                    product_data = await self.extract_product_data(element)
                    if product_data:
                        products.append(product_data)
                except Exception as e:
                    self.logger.error(f"Erro ao extrair produto: {e}")
                    continue
            
            self.logger.info(f"Extraídos {len(products)} produtos válidos")
            
        except Exception as e:
            self.logger.error(f"Erro ao fazer scraping da página: {e}")
        
        return products
    
    async def extract_product_data(self, element) -> Dict[str, Any]:
        """Extrai dados de um produto específico das Americanas"""
        try:
            # Nome do produto
            title_element = await element.query_selector('[data-testid="product-name"]')
            product_name = None
            if title_element:
                product_name = await title_element.inner_text()
                product_name = product_name.strip()
            
            if not product_name:
                return None
            
            # Preço atual
            price_element = await element.query_selector('[data-testid="price-value"]')
            price = None
            if price_element:
                price_text = await price_element.inner_text()
                price = self.extract_price(price_text)
            
            # Preço original (se houver desconto)
            original_price_element = await element.query_selector('[data-testid="price-original"]')
            original_price = None
            if original_price_element:
                original_price_text = await original_price_element.inner_text()
                original_price = self.extract_price(original_price_text)
            
            # URL do produto
            link_element = await element.query_selector('a')
            product_url = None
            if link_element:
                product_url = await link_element.get_attribute('href')
                if product_url and not product_url.startswith('http'):
                    product_url = f"{self.base_url}{product_url}"
                # Remove parâmetros de tracking
                if product_url and '?' in product_url:
                    product_url = product_url.split('?')[0]
            
            # Imagem
            img_element = await element.query_selector('img')
            image_url = None
            if img_element:
                image_url = await img_element.get_attribute('src')
                if image_url and not image_url.startswith('http'):
                    image_url = f"https:{image_url}" if image_url.startswith('//') else f"{self.base_url}{image_url}"
            
            # Desconto percentual
            discount_element = await element.query_selector('[data-testid="discount-percentage"]')
            discount_percentage = None
            if discount_element:
                discount_text = await discount_element.inner_text()
                discount_percentage = self.extract_discount_percentage(discount_text)
            elif price and original_price and original_price > price:
                discount_percentage = round(((original_price - price) / original_price) * 100, 2)
            
            # Avaliação
            rating_element = await element.query_selector('[data-testid="product-rating"]')
            rating = None
            if rating_element:
                rating_text = await rating_element.get_attribute('aria-label') or await rating_element.inner_text()
                rating = self.extract_rating(rating_text)
            
            # Número de avaliações
            reviews_element = await element.query_selector('[data-testid="reviews-count"]')
            reviews_count = None
            if reviews_element:
                reviews_text = await reviews_element.inner_text()
                reviews_count = self.extract_number(reviews_text)
            
            # Categoria (extraída da URL atual)
            category = self.extract_category_from_url(self.page.url)
            
            # Disponibilidade (assume disponível se está na listagem)
            availability = True
            
            # Verifica se tem indicação de indisponibilidade
            unavailable_element = await element.query_selector('[data-testid="unavailable"]')
            if unavailable_element:
                availability = False
            
            return {
                'product_name': product_name,
                'price': price,
                'original_price': original_price,
                'discount_percentage': discount_percentage,
                'url': product_url,
                'image_url': image_url,
                'description': None,  # Americanas não mostra descrição na listagem
                'category': category,
                'brand': None,  # Seria necessário entrar na página do produto
                'availability': availability,
                'rating': rating,
                'reviews_count': reviews_count,
                'additional_data': {
                    'scraped_from': self.page.url,
                    'category_slug': self.extract_category_slug_from_url(self.page.url)
                }
            }
            
        except Exception as e:
            self.logger.error(f"Erro ao extrair dados do produto: {e}")
            return None
    
    def extract_price(self, price_text: str) -> float:
        """Extrai valor numérico do preço"""
        if not price_text:
            return None
        
        # Remove caracteres especiais, mantém apenas números, vírgula e ponto
        price_clean = re.sub(r'[^\d,.]', '', price_text.replace(' ', ''))
        
        # No site das Americanas, o formato é geralmente "R$ 1.234,56"
        if '.' in price_clean and ',' in price_clean:
            # Remove pontos (separadores de milhares) e substitui vírgula por ponto
            price_clean = price_clean.replace('.', '').replace(',', '.')
        elif ',' in price_clean:
            # Se só tem vírgula, substitui por ponto
            price_clean = price_clean.replace(',', '.')
        
        try:
            return float(price_clean)
        except ValueError:
            return None
    
    def extract_discount_percentage(self, discount_text: str) -> float:
        """Extrai percentual de desconto"""
        if not discount_text:
            return None
        
        # Procura por padrão de desconto (ex: "20%", "20% OFF")
        match = re.search(r'(\d+)%', discount_text)
        if match:
            try:
                return float(match.group(1))
            except ValueError:
                return None
        return None
    
    def extract_rating(self, rating_text: str) -> float:
        """Extrai rating numérico"""
        if not rating_text:
            return None
        
        # Procura por padrão de rating (ex: "4.5", "4,5", "4.5 estrelas")
        match = re.search(r'(\d+[.,]\d+|\d+)', rating_text)
        if match:
            rating_str = match.group(1).replace(',', '.')
            try:
                rating = float(rating_str)
                # Garante que o rating está entre 0 e 5
                return min(max(rating, 0), 5)
            except ValueError:
                return None
        return None
    
    def extract_number(self, text: str) -> int:
        """Extrai número inteiro do texto"""
        if not text:
            return None
        
        # Remove caracteres não numéricos
        numbers = re.findall(r'\d+', text)
        if numbers:
            try:
                # Junta todos os números encontrados
                return int(''.join(numbers))
            except ValueError:
                return None
        return None
    
    def extract_category_from_url(self, url: str) -> str:
        """Extrai categoria da URL"""
        try:
            if '/categoria/' in url:
                category_slug = url.split('/categoria/')[1].split('/')[0].split('?')[0]
                # Converte slug para nome mais legível
                category_name = category_slug.replace('-', ' ').title()
                return category_name
            return "Geral"
        except:
            return "Geral"
    
    def extract_category_slug_from_url(self, url: str) -> str:
        """Extrai slug da categoria da URL"""
        try:
            if '/categoria/' in url:
                return url.split('/categoria/')[1].split('/')[0].split('?')[0]
            return "geral"
        except:
            return "geral"
    
    async def navigate_to_page(self, url: str, wait_for: str = None):
        """Sobrescreve navegação para lidar com especificidades das Americanas"""
        try:
            # Headers específicos para as Americanas
            await self.page.set_extra_http_headers({
                **self.get_headers(),
                'Accept-Language': 'pt-BR,pt;q=0.9,en;q=0.8',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none'
            })
            
            # Navega para a página
            await self.page.goto(url, timeout=30000, wait_until='networkidle')
            
            # Aguarda um pouco para garantir que a página carregou
            await self.page.wait_for_timeout(3000)
            
            # Tenta fechar possíveis modais ou popups
            try:
                close_modal = await self.page.query_selector('[data-testid="modal-close"]')
                if close_modal:
                    await close_modal.click()
                    await self.page.wait_for_timeout(1000)
            except:
                pass
            
            self.logger.info(f"Navegou para: {url}")
            
        except Exception as e:
            self.logger.error(f"Erro ao navegar para {url}: {e}")
            raise

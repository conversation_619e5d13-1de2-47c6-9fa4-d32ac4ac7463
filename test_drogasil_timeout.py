#!/usr/bin/env python3
"""
Script de teste para verificar o tratamento de timeout e screenshots no DrogasilScraper
"""

import asyncio
import sys
import os

# Adicionar o diretório raiz ao path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.drogasil_scraper import DrogasilScraper
from utils.logger import get_logger

async def test_drogasil_timeout():
    """Testa o scraper da Drogasil com tratamento de timeout"""
    logger = get_logger("test_drogasil")
    
    # EAN de teste
    test_ean = "7896004703398"
    
    logger.info(f"Iniciando teste do DrogasilScraper com EAN: {test_ean}")
    
    try:
        # Criar instância do scraper
        scraper = DrogasilScraper(eans=[test_ean])
        
        # Executar scraping
        await scraper.run_scraping()
        
        logger.info("Teste concluído com sucesso!")
        
    except Exception as e:
        logger.error(f"Erro durante o teste: {e}")
        raise

if __name__ == "__main__":
    print("=== Teste do DrogasilScraper com Tratamento de Timeout ===")
    print("Este script irá testar:")
    print("1. Navegação para a Drogasil com timeout aumentado")
    print("2. Captura de screenshots em caso de erro")
    print("3. Tratamento específico de timeout")
    print("4. Seletores alternativos em caso de falha")
    print()
    
    # Verificar se a pasta de screenshots existe
    if not os.path.exists("screenshots"):
        os.makedirs("screenshots")
        print("Pasta 'screenshots' criada.")
    
    try:
        asyncio.run(test_drogasil_timeout())
        print("\n✅ Teste executado! Verifique os logs e a pasta 'screenshots' para resultados.")
    except KeyboardInterrupt:
        print("\n❌ Teste interrompido pelo usuário.")
    except Exception as e:
        print(f"\n❌ Erro durante o teste: {e}")
        print("Verifique a pasta 'screenshots' para screenshots de erro.")

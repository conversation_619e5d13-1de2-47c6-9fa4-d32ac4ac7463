from typing import List, Dict, Any
from core.base_scraper import BaseScraper
import re

class ExampleScraper(BaseScraper):
    """Exemplo de scraper para um site de e-commerce"""
    
    def __init__(self):
        super().__init__("example_site")
        self.base_url = "https://example-ecommerce.com"
    
    def get_urls_to_scrape(self) -> List[str]:
        """Retorna URLs para fazer scraping"""
        # Exemplo: URLs de categorias ou páginas de produtos
        return [
            f"{self.base_url}/categoria/eletronicos",
            f"{self.base_url}/categoria/roupas",
            f"{self.base_url}/categoria/casa",
        ]
    
    async def scrape(self) -> List[Dict[str, Any]]:
        """Extrai dados da página atual"""
        products = []
        
        try:
            # Aguarda os produtos carregarem
            await self.page.wait_for_selector('.product-item', timeout=10000)
            
            # Obtém todos os elementos de produto
            product_elements = await self.page.query_selector_all('.product-item')
            
            for element in product_elements:
                try:
                    product_data = await self.extract_product_data(element)
                    if product_data:
                        products.append(product_data)
                except Exception as e:
                    self.logger.error(f"Erro ao extrair produto: {e}")
                    continue
            
            self.logger.info(f"Extraídos {len(products)} produtos da página")
            
        except Exception as e:
            self.logger.error(f"Erro ao fazer scraping da página: {e}")
        
        return products
    
    async def extract_product_data(self, element) -> Dict[str, Any]:
        """Extrai dados de um produto específico"""
        try:
            # Nome do produto
            name_element = await element.query_selector('.product-name')
            product_name = await name_element.inner_text() if name_element else None
            
            # Preço atual
            price_element = await element.query_selector('.price-current')
            price_text = await price_element.inner_text() if price_element else None
            price = self.extract_price(price_text) if price_text else None
            
            # Preço original (se houver desconto)
            original_price_element = await element.query_selector('.price-original')
            original_price_text = await original_price_element.inner_text() if original_price_element else None
            original_price = self.extract_price(original_price_text) if original_price_text else None
            
            # URL do produto
            link_element = await element.query_selector('a')
            product_url = await link_element.get_attribute('href') if link_element else None
            if product_url and not product_url.startswith('http'):
                product_url = f"{self.base_url}{product_url}"
            
            # Imagem
            img_element = await element.query_selector('img')
            image_url = await img_element.get_attribute('src') if img_element else None
            if image_url and not image_url.startswith('http'):
                image_url = f"{self.base_url}{image_url}"
            
            # Descrição
            desc_element = await element.query_selector('.product-description')
            description = await desc_element.inner_text() if desc_element else None
            
            # Categoria (pode ser extraída da URL ou de um elemento específico)
            category_element = await element.query_selector('.product-category')
            category = await category_element.inner_text() if category_element else None
            
            # Marca
            brand_element = await element.query_selector('.product-brand')
            brand = await brand_element.inner_text() if brand_element else None
            
            # Disponibilidade
            availability_element = await element.query_selector('.availability')
            availability_text = await availability_element.inner_text() if availability_element else "Em estoque"
            availability = "indisponível" not in availability_text.lower()
            
            # Rating
            rating_element = await element.query_selector('.rating')
            rating = None
            if rating_element:
                rating_text = await rating_element.inner_text()
                rating = self.extract_rating(rating_text)
            
            # Número de avaliações
            reviews_element = await element.query_selector('.reviews-count')
            reviews_count = None
            if reviews_element:
                reviews_text = await reviews_element.inner_text()
                reviews_count = self.extract_number(reviews_text)
            
            # Calcula desconto se houver preço original
            discount_percentage = None
            if price and original_price and original_price > price:
                discount_percentage = round(((original_price - price) / original_price) * 100, 2)
            
            return {
                'product_name': product_name,
                'price': price,
                'original_price': original_price,
                'discount_percentage': discount_percentage,
                'url': product_url,
                'image_url': image_url,
                'description': description,
                'category': category,
                'brand': brand,
                'availability': availability,
                'rating': rating,
                'reviews_count': reviews_count,
                'additional_data': {
                    'scraped_from': self.page.url,
                    'page_title': await self.page.title()
                }
            }
            
        except Exception as e:
            self.logger.error(f"Erro ao extrair dados do produto: {e}")
            return None
    
    def extract_price(self, price_text: str) -> float:
        """Extrai valor numérico do preço"""
        if not price_text:
            return None
        
        # Remove caracteres não numéricos exceto vírgula e ponto
        price_clean = re.sub(r'[^\d,.]', '', price_text)
        
        # Substitui vírgula por ponto se for separador decimal
        if ',' in price_clean and '.' in price_clean:
            # Se tem ambos, vírgula é separador de milhares
            price_clean = price_clean.replace(',', '')
        elif ',' in price_clean:
            # Se só tem vírgula, pode ser separador decimal
            parts = price_clean.split(',')
            if len(parts) == 2 and len(parts[1]) <= 2:
                price_clean = price_clean.replace(',', '.')
        
        try:
            return float(price_clean)
        except ValueError:
            return None
    
    def extract_rating(self, rating_text: str) -> float:
        """Extrai rating numérico"""
        if not rating_text:
            return None
        
        # Procura por padrão de rating (ex: "4.5", "4,5", "4.5/5")
        match = re.search(r'(\d+[.,]\d+|\d+)', rating_text)
        if match:
            rating_str = match.group(1).replace(',', '.')
            try:
                return float(rating_str)
            except ValueError:
                return None
        return None
    
    def extract_number(self, text: str) -> int:
        """Extrai número inteiro do texto"""
        if not text:
            return None
        
        # Remove caracteres não numéricos
        numbers = re.findall(r'\d+', text)
        if numbers:
            try:
                return int(''.join(numbers))
            except ValueError:
                return None
        return None

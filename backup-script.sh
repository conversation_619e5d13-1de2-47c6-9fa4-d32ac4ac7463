#!/bin/bash

# Script de backup automático do banco de dados
set -e

# Configurações
DB_HOST="postgres"
DB_PORT="5432"
DB_NAME="pricenow"
DB_USER="postgres"
BACKUP_DIR="/backups"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/pricenow_backup_$DATE.sql"

# Criar diretório de backup se não existir
mkdir -p $BACKUP_DIR

echo "Iniciando backup do banco de dados..."
echo "Data: $(date)"
echo "Arquivo: $BACKUP_FILE"

# Fazer backup
pg_dump -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME > $BACKUP_FILE

# Comprimir backup
gzip $BACKUP_FILE

echo "Backup concluído: ${BACKUP_FILE}.gz"

# Remover backups antigos (manter apenas os últimos 7 dias)
find $BACKUP_DIR -name "pricenow_backup_*.sql.gz" -mtime +7 -delete

echo "Backups antigos removidos"
echo "Backup finalizado com sucesso!"

# Listar backups existentes
echo "Backups disponíveis:"
ls -la $BACKUP_DIR/pricenow_backup_*.sql.gz 2>/dev/null || echo "Nenhum backup encontrado"

"""
Configurações específicas para cada scraper
"""

from typing import Dict, Any, List

class ScraperConfig:
    """Configuração base para scrapers"""

    def __init__(self, name: str, base_url: str, enabled: bool = True, **kwargs):
        self.name = name
        self.base_url = base_url
        self.enabled = enabled
        self.config = kwargs

    def get(self, key: str, default=None):
        """Obtém valor de configuração"""
        return self.config.get(key, default)

    def is_enabled(self) -> bool:
        """Verifica se o scraper está habilitado"""
        return self.enabled

# Configurações específicas para cada site
SCRAPER_CONFIGS = {
    "mercadolivre": ScraperConfig(
        name="mercadolivre",
        base_url="https://www.mercadolivre.com.br",
        enabled=False,  # DESABILITADO
        search_terms=[
            "smartphone",
            "notebook", 
            "tablet",
            "fone de ouvido",
            "smartwatch",
            "televisao",
            "geladeira",
            "micro-ondas",
            "ar condicionado",
            "maquina de lavar"
        ],
        selectors={
            "product_container": ".ui-search-result",
            "product_title": ".ui-search-item__title",
            "price_current": ".andes-money-amount__fraction",
            "price_original": ".ui-search-price__second-line .andes-money-amount__fraction",
            "product_link": ".ui-search-item__group__element a",
            "product_image": ".ui-search-result-image__element img",
            "shipping": ".ui-search-item__shipping",
            "seller": ".ui-search-item__group__element--seller",
            "location": ".ui-search-item__group__element--location",
            "rating": ".ui-search-reviews__rating-number",
            "reviews_count": ".ui-search-reviews__amount"
        },
        headers={
            "Accept-Language": "pt-BR,pt;q=0.9,en;q=0.8",
            "Cache-Control": "no-cache",
            "Pragma": "no-cache"
        },
        delays={
            "page_load": 2000,
            "between_requests": 1000
        }
    ),
    
    "americanas": ScraperConfig(
        enabled=False,  # DESABILITADO
        name="americanas",
        base_url="https://www.americanas.com.br",
        categories=[
            "celulares-e-smartphones",
            "notebooks",
            "tablets", 
            "fones-de-ouvido",
            "smartwatch-e-relogios-inteligentes",
            "tv-e-home-theater",
            "refrigeradores",
            "micro-ondas",
            "games",
            "livros"
        ],
        selectors={
            "product_container": '[data-testid="product-card"]',
            "product_title": '[data-testid="product-name"]',
            "price_current": '[data-testid="price-value"]',
            "price_original": '[data-testid="price-original"]',
            "product_link": "a",
            "product_image": "img",
            "discount": '[data-testid="discount-percentage"]',
            "rating": '[data-testid="product-rating"]',
            "reviews_count": '[data-testid="reviews-count"]',
            "unavailable": '[data-testid="unavailable"]'
        },
        headers={
            "Accept-Language": "pt-BR,pt;q=0.9,en;q=0.8",
            "Cache-Control": "no-cache",
            "Pragma": "no-cache",
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "none"
        },
        delays={
            "page_load": 3000,
            "between_requests": 1500,
            "modal_close": 1000
        }
    ),
    
    "amazon": ScraperConfig(
        enabled=False,  # DESABILITADO
        name="amazon",
        base_url="https://www.amazon.com.br",
        search_terms=[
            "smartphone",
            "notebook",
            "tablet",
            "fone de ouvido",
            "smartwatch",
            "tv",
            "geladeira",
            "micro-ondas"
        ],
        selectors={
            "product_container": '[data-component-type="s-search-result"]',
            "product_title": 'h2 a span',
            "price_current": '.a-price-whole',
            "price_original": '.a-price.a-text-price .a-offscreen',
            "product_link": 'h2 a',
            "product_image": '.s-image',
            "rating": '.a-icon-alt',
            "reviews_count": '.a-size-base'
        },
        headers={
            "Accept-Language": "pt-BR,pt;q=0.9,en;q=0.8",
            "Cache-Control": "no-cache"
        },
        delays={
            "page_load": 2000,
            "between_requests": 2000
        }
    ),
    
    "magazine_luiza": ScraperConfig(
        enabled=False,  # DESABILITADO
        name="magazine_luiza",
        base_url="https://www.magazineluiza.com.br",
        categories=[
            "celular-e-smartphone",
            "notebook",
            "tablet",
            "fone-de-ouvido",
            "smartwatch",
            "tv",
            "geladeira",
            "micro-ondas"
        ],
        selectors={
            "product_container": '[data-testid="product-card"]',
            "product_title": '[data-testid="product-title"]',
            "price_current": '[data-testid="price-value"]',
            "price_original": '[data-testid="price-original"]',
            "product_link": "a",
            "product_image": "img"
        },
        headers={
            "Accept-Language": "pt-BR,pt;q=0.9,en;q=0.8"
        },
        delays={
            "page_load": 2500,
            "between_requests": 1500
        }
    ),

    "drogasil": ScraperConfig(
        enabled=true,  # DESABILITADO - EXECUÇÃO MANUAL APENAS
        name="drogasil",
        base_url="https://www.drogasil.com.br",
        search_type="ean",  # Busca por EAN
        default_eans=[
            "7896004703398",  # Dipirona
        ],
        selectors={
            "product_container": ".showcase-item, .product-item, .item-product, [data-testid='product-card']",
            "product_title": ".product-name, .product-title, .item-name, .title, h2, h3",
            "price_current": ".price-current, .price-value, .current-price, .price",
            "price_original": ".price-original, .price-old, .old-price, .price-from",
            "product_link": "a",
            "product_image": "img",
            "discount": ".discount, .desconto, .off",
            "brand": ".brand, .marca, .laboratory, .laboratorio",
            "unavailable": ".unavailable, .out-of-stock, .indisponivel"
        },
        headers={
            "Accept-Language": "pt-BR,pt;q=0.9,en;q=0.8",
            "Cache-Control": "no-cache",
            "Pragma": "no-cache",
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "none"
        },
        delays={
            "page_load": 3000,
            "between_requests": 2000,
            "modal_close": 1000
        }
    )
}

def get_scraper_config(scraper_name: str) -> ScraperConfig:
    """Retorna configuração para um scraper específico"""
    return SCRAPER_CONFIGS.get(scraper_name)

def get_available_scrapers() -> List[str]:
    """Retorna lista de scrapers configurados"""
    return list(SCRAPER_CONFIGS.keys())

def get_scraper_selectors(scraper_name: str) -> Dict[str, str]:
    """Retorna seletores CSS para um scraper"""
    config = get_scraper_config(scraper_name)
    return config.get("selectors", {}) if config else {}

def get_scraper_headers(scraper_name: str) -> Dict[str, str]:
    """Retorna headers para um scraper"""
    config = get_scraper_config(scraper_name)
    return config.get("headers", {}) if config else {}

def get_scraper_delays(scraper_name: str) -> Dict[str, int]:
    """Retorna delays para um scraper"""
    config = get_scraper_config(scraper_name)
    return config.get("delays", {}) if config else {}

# Configurações globais para todos os scrapers
GLOBAL_CONFIG = {
    "user_agents": [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    ],
    "default_timeout": 30000,
    "default_delay": 1000,
    "max_retries": 3,
    "viewport": {
        "width": 1920,
        "height": 1080
    }
}

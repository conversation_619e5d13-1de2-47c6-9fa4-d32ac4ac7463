#!/bin/bash
set -e

# Iniciar Xvfb para modo headless se necessário
if [ "$PLAYWRIGHT_HEADLESS" != "true" ]; then
    echo "Iniciando Xvfb para modo não-headless..."
    Xvfb :99 -screen 0 1920x1080x24 &
    export DISPLAY=:99
fi

# Aguardar banco de dados estar disponível
echo "Aguardando banco de dados..."
while ! nc -z postgres 5432; do
    sleep 1
done
echo "Banco de dados disponível!"

# Inicializar banco se necessário
echo "Inicializando banco de dados..."
python main.py init || echo "Banco já inicializado"

# Executar comando passado
echo "Executando: $@"
exec "$@"

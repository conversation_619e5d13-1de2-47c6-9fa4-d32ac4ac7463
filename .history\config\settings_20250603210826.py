import os
from typing import Optional
from pydantic_settings import BaseSettings
from pydantic import Field
from dotenv import load_dotenv

load_dotenv()

class Settings(BaseSettings):
    # Database
    DATABASE_URL: str = Field(
        default="postgresql://postgres:password@localhost:5432/pricenow",
        description="Database connection URL"
    )

    # API
    API_HOST: str = Field(default="0.0.0.0", description="API host")
    API_PORT: int = Field(default=8000, description="API port")
    API_TITLE: str = Field(default="PriceNow Web Scraping API", description="API title")
    API_VERSION: str = Field(default="1.0.0", description="API version")

    # Playwright
    PLAYWRIGHT_HEADLESS: bool = Field(default=True, description="Run Playwright in headless mode")
    PLAYWRIGHT_TIMEOUT: int = Field(default=60000, description="Playwright timeout in milliseconds")

    # Scraping
    SCRAPING_DELAY: float = Field(default=1.0, description="Delay between scraping requests")
    MAX_RETRIES: int = Field(default=3, description="Maximum number of retries")

    # Screenshots
    SCREENSHOTS_ENABLED: bool = Field(default=True, description="Enable screenshots on errors")
    SCREENSHOTS_DIR: str = Field(default="screenshots", description="Directory to save error screenshots")

    # Scrapers habilitados/desabilitados
    SCRAPERS_ENABLED: str = Field(default="example,drogasil", description="Comma-separated list of enabled scrapers")
    MERCADOLIVRE_ENABLED: bool = Field(default=False, description="Enable MercadoLivre scraper")
    AMERICANAS_ENABLED: bool = Field(default=False, description="Enable Americanas scraper")
    AMAZON_ENABLED: bool = Field(default=False, description="Enable Amazon scraper")
    DROGASIL_ENABLED: bool = Field(default=True, description="Enable Drogasil scraper")
    EXAMPLE_ENABLED: bool = Field(default=True, description="Enable Example scraper")

    # Logging
    LOG_LEVEL: str = Field(default="INFO", description="Logging level")
    LOG_FILE: str = Field(default="logs/scraper.log", description="Log file path")

    model_config = {
        "env_file": ".env",
        "env_file_encoding": "utf-8",
        "case_sensitive": True
    }

settings = Settings()

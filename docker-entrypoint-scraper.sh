#!/bin/bash
# Não usar set -e para evitar exit em erros não críticos

echo "🚀 Iniciando container do scraper..."

# Função para log com timestamp
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Função para aguardar serviço com retry
wait_for_service() {
    local host=$1
    local port=$2
    local service_name=$3
    local timeout=${4:-60}
    local counter=0

    log "🔍 Aguardando $service_name ($host:$port)..."

    while ! nc -z "$host" "$port" 2>/dev/null; do
        sleep 2
        counter=$((counter + 2))
        if [ $counter -ge $timeout ]; then
            log "⚠️  Timeout aguardando $service_name após ${timeout}s - continuando mesmo assim"
            return 1
        fi
        if [ $((counter % 10)) -eq 0 ]; then
            log "⏳ Ainda aguardando $service_name... (${counter}s/${timeout}s)"
        fi
    done

    log "✅ $service_name disponível!"
    return 0
}

# Verificar modo do scraper
if [ "$SCRAPER_MODE" = "standby" ]; then
    log "📋 Modo: STANDBY - Scrapers desabilitados"
    log "🔧 Para executar manualmente: docker exec pricenow_scraper python main.py scrape --scraper <nome>"
    log "💤 Container ficará em standby..."
    exec "$@"
fi

# Iniciar Xvfb para modo headless se necessário
if [ "$PLAYWRIGHT_HEADLESS" != "true" ]; then
    log "🖥️  Iniciando Xvfb para modo não-headless..."
    Xvfb :99 -screen 0 1920x1080x24 > /dev/null 2>&1 &
    export DISPLAY=:99
    sleep 2
fi

# Aguardar dependências
wait_for_service "postgres" "5432" "PostgreSQL" 90
wait_for_service "api" "8000" "API" 60

# Inicializar banco se necessário (sem falhar se der erro)
log "🔧 Verificando inicialização do banco..."
if timeout 30 python main.py init 2>/dev/null; then
    log "✅ Banco inicializado com sucesso"
else
    log "⚠️  Banco já inicializado ou erro na inicialização - continuando"
fi

# Verificar se todos os scrapers estão desabilitados
if [ "$AUTO_SCRAPING_ENABLED" = "false" ] || [ -z "$SCRAPERS_ENABLED" ]; then
    log "🚫 Execução automática desabilitada"
    log "📋 Scrapers configurados: ${SCRAPERS_ENABLED:-'nenhum'}"
    log "🔧 Para executar manualmente: docker exec pricenow_scraper python main.py scrape --scraper <nome>"

    # Se o comando for scrape --all, substituir por standby
    if echo "$*" | grep -q "scrape.*--all"; then
        log "🔄 Comando 'scrape --all' detectado, mas scrapers desabilitados"
        log "💤 Entrando em modo standby..."
        exec sleep infinity
    fi
fi

# Executar comando passado
log "▶️  Executando: $*"
exec "$@"

# Docker Compose para desenvolvimento
version: '3.8'

services:
  # Banco de dados PostgreSQL
  postgres:
    image: postgres:15
    container_name: pricenow_postgres_dev
    environment:
      POSTGRES_DB: pricenow_dev
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
    restart: unless-stopped

  # Cache Redis
  redis:
    image: redis:7-alpine
    container_name: pricenow_redis_dev
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    restart: unless-stopped

  # PgAdmin para desenvolvimento
  pgadmin:
    image: dpage/pgadmin4
    container_name: pricenow_pgadmin_dev
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "8080:80"
    volumes:
      - pgadmin_dev_data:/var/lib/pgadmin
    depends_on:
      - postgres
    restart: unless-stopped

volumes:
  postgres_dev_data:
  redis_dev_data:
  pgadmin_dev_data:

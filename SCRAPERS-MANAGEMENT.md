# Gerenciamento de Scrapers - PriceNow

## Mercado Livre Desabilitado ✅

O scraper do Mercado Livre foi **desabilitado** conforme solicitado. Este documento explica como o sistema de habilitação/desabilitação funciona.

## Como Funciona

### 1. Configuração Dupla
O sistema usa duas camadas de configuração para máxima flexibilidade:

**Arquivo `config/settings.py`:**
- Configurações via variáveis de ambiente
- Controle individual por scraper
- Lista geral de scrapers habilitados

**Arquivo `config/scrapers.py`:**
- Configurações específicas de cada scraper
- Parâmetro `enabled=False` para desabilitar

### 2. Status Atual dos Scrapers

| Scraper | Status | Configuração |
|---------|--------|--------------|
| **Mercado Livre** | ❌ **DESABILITADO** | `enabled=False` |
| Drogasil | ✅ Habilitado | `enabled=True` |
| Example | ✅ Habilitado | `enabled=True` |
| Americanas | ❌ Desabilitado | Não registrado |
| Amazon | ❌ Desabilitado | Não registrado |

## Como Habilitar/Desabilitar Scrapers

### Método 1: Arquivo `.env`
Crie ou edite o arquivo `.env` na raiz do projeto:

```env
# Desabilitar Mercado Livre
MERCADOLIVRE_ENABLED=false

# Habilitar outros scrapers
DROGASIL_ENABLED=true
EXAMPLE_ENABLED=true

# Lista geral (alternativa)
SCRAPERS_ENABLED=example,drogasil
```

### Método 2: Configuração Direta
Edite `config/scrapers.py`:

```python
"mercadolivre": ScraperConfig(
    name="mercadolivre",
    base_url="https://www.mercadolivre.com.br",
    enabled=False,  # DESABILITADO
    # ... outras configurações
),
```

### Método 3: Variáveis de Ambiente
```bash
export MERCADOLIVRE_ENABLED=false
export DROGASIL_ENABLED=true
```

## Verificar Status dos Scrapers

### Comando de Listagem
```bash
python main.py list-scrapers
```

### Script de Teste Completo
```bash
python test_scrapers_status.py
```

Este script mostra:
- Status de cada scraper
- Configurações ativas
- Scrapers registrados
- Verificação específica do Mercado Livre

## Executar Scrapers

### Executar Scraper Específico
```bash
# Apenas scrapers habilitados funcionarão
python main.py scrape --scraper drogasil
```

### Executar Todos os Scrapers Habilitados
```bash
python main.py scrape --all
```

### Tentar Executar Scraper Desabilitado
```bash
# Isso falhará com erro
python main.py scrape --scraper mercadolivre
# Erro: Scraper 'mercadolivre' não encontrado
```

## Logs de Scrapers

O sistema registra nos logs quando scrapers são habilitados/desabilitados:

```
INFO - Scraper 'drogasil' registrado
INFO - Scraper 'mercadolivre' desabilitado
INFO - Scrapers registrados: ['example', 'drogasil']
```

## Reabilitar Mercado Livre

Se precisar reabilitar o Mercado Livre no futuro:

### 1. Via `.env`
```env
MERCADOLIVRE_ENABLED=true
```

### 2. Via `config/scrapers.py`
```python
"mercadolivre": ScraperConfig(
    name="mercadolivre",
    base_url="https://www.mercadolivre.com.br",
    enabled=True,  # HABILITADO
    # ...
),
```

### 3. Reiniciar aplicação
```bash
# Testar
python test_scrapers_status.py

# Executar
python main.py scrape --scraper mercadolivre
```

## Adicionar Novos Scrapers

Para adicionar um novo scraper:

### 1. Criar o arquivo do scraper
```python
# services/novo_scraper.py
from core.base_scraper import BaseScraper

class NovoScraper(BaseScraper):
    # implementação...
```

### 2. Adicionar configuração
```python
# config/scrapers.py
"novo": ScraperConfig(
    name="novo",
    base_url="https://exemplo.com",
    enabled=True,  # ou False para desabilitar
    # configurações específicas...
),
```

### 3. Registrar no main.py
```python
# main.py
if settings.is_scraper_enabled('novo'):
    self.scrapers['novo'] = NovoScraper
```

### 4. Adicionar configuração de ambiente
```python
# config/settings.py
NOVO_ENABLED: bool = Field(default=True, description="Enable Novo scraper")
```

## Troubleshooting

### Scraper não aparece na lista
1. Verificar se está habilitado no `.env`
2. Verificar configuração em `scrapers.py`
3. Verificar se foi registrado no `main.py`
4. Executar `python test_scrapers_status.py`

### Erro ao executar scraper
1. Verificar se o scraper está na lista de disponíveis
2. Verificar logs para mensagens de erro
3. Verificar dependências do scraper específico

## Benefícios do Sistema

✅ **Controle Granular**: Habilitar/desabilitar scrapers individualmente
✅ **Flexibilidade**: Múltiplas formas de configuração
✅ **Segurança**: Scrapers desabilitados não são registrados
✅ **Logs Claros**: Visibilidade do status de cada scraper
✅ **Fácil Manutenção**: Configuração centralizada

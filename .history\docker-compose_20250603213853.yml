version: '3.8'

services:
  # Banco de dados PostgreSQL
  postgres:
    image: postgres:15
    container_name: pricenow_postgres
    environment:
      POSTGRES_DB: pricenow
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Cache Redis
  redis:
    image: redis:7-alpine
    container_name: pricenow_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # API da aplicação
  api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: pricenow_api
    environment:
      - DATABASE_URL=********************************************/pricenow
      - API_HOST=0.0.0.0
      - API_PORT=8000
      - PLAYWRIGHT_HEADLESS=true
      - LOG_LEVEL=INFO
    ports:
      - "8000:8000"
    volumes:
      - ./logs:/app/logs
      - ./.env:/app/.env:ro
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Scraper de dados - MODO STANDBY (execução manual apenas)
  scraper:
    build:
      context: .
      dockerfile: Dockerfile.scraper
    container_name: pricenow_scraper
    environment:
      - DATABASE_URL=********************************************/pricenow
      - PLAYWRIGHT_HEADLESS=true
      - SCRAPING_DELAY=2.0
      - MAX_RETRIES=3
      - LOG_LEVEL=INFO
      - AUTO_SCRAPING_ENABLED=false
      - SCRAPERS_ENABLED=
      - SCRAPER_MODE=standby  # Modo standby - não executar scrapers automaticamente
    volumes:
      - ./logs:/app/logs
      - ./screenshots:/app/screenshots
      - ./.env:/app/.env:ro
    depends_on:
      postgres:
        condition: service_healthy
      api:
        condition: service_healthy
    restart: unless-stopped
    # Sobrescrever o CMD padrão para modo standby
    command: ["sleep", "infinity"]
    # Para executar manualmente: docker exec pricenow_scraper python main.py scrape --scraper <nome>
    healthcheck:
      test: ["CMD", "pgrep", "-f", "sleep"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PgAdmin para gerenciar banco
  pgadmin:
    image: dpage/pgadmin4
    container_name: pricenow_pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "8080:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      - postgres
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  pgadmin_data:

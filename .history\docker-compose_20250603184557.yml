version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: pricenow_postgres
    environment:
      POSTGRES_DB: pricenow
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: pricenow_redis
    ports:
      - "6379:6379"
    restart: unless-stopped

  pgadmin:
    image: dpage/pgadmin4
    container_name: pricenow_pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
    ports:
      - "8080:80"
    depends_on:
      - postgres
    restart: unless-stopped

volumes:
  postgres_data:

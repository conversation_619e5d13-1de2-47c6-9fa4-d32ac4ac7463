from fastapi import FastAPI, Depends, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime, timedelta
from database.connection import get_db, DatabaseManager
from database.models import ScrapedData, ScrapingJob, SiteConfig
from api.schemas import (
    ScrapedDataResponse, 
    ScrapingJobResponse, 
    SiteConfigResponse,
    ProductFilter,
    PaginationParams
)
from config.settings import settings
from utils.logger import get_logger

# Inicializa o logger
logger = get_logger("api")

# Cria a aplicação FastAPI
app = FastAPI(
    title=settings.API_TITLE,
    version=settings.API_VERSION,
    description="API para acessar dados de web scraping de preços"
)

# Configuração CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Em produção, especificar domínios específicos
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.on_event("startup")
async def startup_event():
    """Inicializa o banco de dados na inicialização da API"""
    try:
        DatabaseManager.init_db()
        logger.info("Banco de dados inicializado")
    except Exception as e:
        logger.error(f"Erro ao inicializar banco de dados: {e}")

@app.get("/")
async def root():
    """Endpoint raiz da API"""
    return {
        "message": "PriceNow Web Scraping API",
        "version": settings.API_VERSION,
        "status": "running"
    }

@app.get("/health")
async def health_check():
    """Endpoint de health check"""
    return {"status": "healthy", "timestamp": datetime.utcnow()}

@app.get("/products", response_model=List[ScrapedDataResponse])
async def get_products(
    site_name: Optional[str] = Query(None, description="Filtrar por site"),
    category: Optional[str] = Query(None, description="Filtrar por categoria"),
    brand: Optional[str] = Query(None, description="Filtrar por marca"),
    min_price: Optional[float] = Query(None, description="Preço mínimo"),
    max_price: Optional[float] = Query(None, description="Preço máximo"),
    availability: Optional[bool] = Query(None, description="Filtrar por disponibilidade"),
    search: Optional[str] = Query(None, description="Buscar no nome do produto"),
    page: int = Query(1, ge=1, description="Número da página"),
    size: int = Query(50, ge=1, le=100, description="Itens por página"),
    db: Session = Depends(get_db)
):
    """Retorna lista de produtos com filtros opcionais"""
    try:
        query = db.query(ScrapedData)
        
        # Aplicar filtros
        if site_name:
            query = query.filter(ScrapedData.site_name == site_name)
        
        if category:
            query = query.filter(ScrapedData.category.ilike(f"%{category}%"))
        
        if brand:
            query = query.filter(ScrapedData.brand.ilike(f"%{brand}%"))
        
        if min_price is not None:
            query = query.filter(ScrapedData.price >= min_price)
        
        if max_price is not None:
            query = query.filter(ScrapedData.price <= max_price)
        
        if availability is not None:
            query = query.filter(ScrapedData.availability == availability)
        
        if search:
            query = query.filter(ScrapedData.product_name.ilike(f"%{search}%"))
        
        # Ordenar por data de scraping (mais recente primeiro)
        query = query.order_by(ScrapedData.scraped_at.desc())
        
        # Paginação
        offset = (page - 1) * size
        products = query.offset(offset).limit(size).all()
        
        logger.info(f"Retornados {len(products)} produtos")
        return products
        
    except Exception as e:
        logger.error(f"Erro ao buscar produtos: {e}")
        raise HTTPException(status_code=500, detail="Erro interno do servidor")

@app.get("/products/{product_id}", response_model=ScrapedDataResponse)
async def get_product(product_id: int, db: Session = Depends(get_db)):
    """Retorna um produto específico por ID"""
    try:
        product = db.query(ScrapedData).filter(ScrapedData.id == product_id).first()
        if not product:
            raise HTTPException(status_code=404, detail="Produto não encontrado")
        
        logger.info(f"Produto {product_id} encontrado")
        return product
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Erro ao buscar produto {product_id}: {e}")
        raise HTTPException(status_code=500, detail="Erro interno do servidor")

@app.get("/sites", response_model=List[str])
async def get_sites(db: Session = Depends(get_db)):
    """Retorna lista de sites disponíveis"""
    try:
        sites = db.query(ScrapedData.site_name).distinct().all()
        site_names = [site[0] for site in sites]
        
        logger.info(f"Retornados {len(site_names)} sites")
        return site_names
        
    except Exception as e:
        logger.error(f"Erro ao buscar sites: {e}")
        raise HTTPException(status_code=500, detail="Erro interno do servidor")

@app.get("/categories", response_model=List[str])
async def get_categories(
    site_name: Optional[str] = Query(None, description="Filtrar por site"),
    db: Session = Depends(get_db)
):
    """Retorna lista de categorias disponíveis"""
    try:
        query = db.query(ScrapedData.category).filter(ScrapedData.category.isnot(None)).distinct()
        
        if site_name:
            query = query.filter(ScrapedData.site_name == site_name)
        
        categories = query.all()
        category_names = [cat[0] for cat in categories if cat[0]]
        
        logger.info(f"Retornadas {len(category_names)} categorias")
        return category_names
        
    except Exception as e:
        logger.error(f"Erro ao buscar categorias: {e}")
        raise HTTPException(status_code=500, detail="Erro interno do servidor")

@app.get("/brands", response_model=List[str])
async def get_brands(
    site_name: Optional[str] = Query(None, description="Filtrar por site"),
    category: Optional[str] = Query(None, description="Filtrar por categoria"),
    db: Session = Depends(get_db)
):
    """Retorna lista de marcas disponíveis"""
    try:
        query = db.query(ScrapedData.brand).filter(ScrapedData.brand.isnot(None)).distinct()
        
        if site_name:
            query = query.filter(ScrapedData.site_name == site_name)
        
        if category:
            query = query.filter(ScrapedData.category.ilike(f"%{category}%"))
        
        brands = query.all()
        brand_names = [brand[0] for brand in brands if brand[0]]
        
        logger.info(f"Retornadas {len(brand_names)} marcas")
        return brand_names
        
    except Exception as e:
        logger.error(f"Erro ao buscar marcas: {e}")
        raise HTTPException(status_code=500, detail="Erro interno do servidor")

@app.get("/jobs", response_model=List[ScrapingJobResponse])
async def get_scraping_jobs(
    site_name: Optional[str] = Query(None, description="Filtrar por site"),
    status: Optional[str] = Query(None, description="Filtrar por status"),
    page: int = Query(1, ge=1, description="Número da página"),
    size: int = Query(20, ge=1, le=100, description="Itens por página"),
    db: Session = Depends(get_db)
):
    """Retorna lista de jobs de scraping"""
    try:
        query = db.query(ScrapingJob)
        
        if site_name:
            query = query.filter(ScrapingJob.site_name == site_name)
        
        if status:
            query = query.filter(ScrapingJob.status == status)
        
        # Ordenar por data de criação (mais recente primeiro)
        query = query.order_by(ScrapingJob.created_at.desc())
        
        # Paginação
        offset = (page - 1) * size
        jobs = query.offset(offset).limit(size).all()
        
        logger.info(f"Retornados {len(jobs)} jobs")
        return jobs
        
    except Exception as e:
        logger.error(f"Erro ao buscar jobs: {e}")
        raise HTTPException(status_code=500, detail="Erro interno do servidor")

@app.get("/stats")
async def get_statistics(db: Session = Depends(get_db)):
    """Retorna estatísticas gerais do sistema"""
    try:
        # Total de produtos
        total_products = db.query(ScrapedData).count()
        
        # Produtos por site
        products_by_site = db.query(
            ScrapedData.site_name,
            db.func.count(ScrapedData.id).label('count')
        ).group_by(ScrapedData.site_name).all()
        
        # Jobs recentes (últimas 24 horas)
        yesterday = datetime.utcnow() - timedelta(days=1)
        recent_jobs = db.query(ScrapingJob).filter(
            ScrapingJob.created_at >= yesterday
        ).count()
        
        # Produtos adicionados hoje
        today = datetime.utcnow().date()
        products_today = db.query(ScrapedData).filter(
            db.func.date(ScrapedData.scraped_at) == today
        ).count()
        
        stats = {
            "total_products": total_products,
            "products_by_site": {site: count for site, count in products_by_site},
            "recent_jobs_24h": recent_jobs,
            "products_added_today": products_today,
            "last_updated": datetime.utcnow()
        }
        
        logger.info("Estatísticas calculadas")
        return stats
        
    except Exception as e:
        logger.error(f"Erro ao calcular estatísticas: {e}")
        raise HTTPException(status_code=500, detail="Erro interno do servidor")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "api.main:app",
        host=settings.API_HOST,
        port=settings.API_PORT,
        reload=True
    )

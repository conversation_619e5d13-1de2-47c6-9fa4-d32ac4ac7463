# Fix para Timeout e Screenshots - Drogasil Scraper

## Problema Identificado
O scraper da Drogasil estava apresentando timeouts frequentes, sem captura de informações sobre o que estava acontecendo no momento do erro.

## Soluções Implementadas

### 1. Aumento de Timeouts
- **Timeout geral do Playwright**: Aumentado de 30s para 60s
- **Timeout específico para produtos**: Aumentado para 60s
- **Timeout de navegação**: 60s com `wait_until="domcontentloaded"`

### 2. Sistema de Screenshots Automáticos
- **Pasta de screenshots**: Criada pasta `screenshots/` na raiz do projeto
- **Captura automática**: Screenshots são capturados automaticamente em caso de erro
- **Nomenclatura**: `{site_name}_{error_context}_{timestamp}.png`
- **Configurável**: Pode ser habilitado/desabilitado via `SCREENSHOTS_ENABLED` nas configurações

### 3. Tratamento Específico de Timeout
- **Detecção de timeout**: Captura específica de `PlaywrightTimeoutError`
- **Seletores alternativos**: Tentativa com seletores diferentes se o principal falhar
- **Logs detalhados**: Informações específicas sobre cada tipo de erro

### 4. Navegação Melhorada para Drogasil
- **Método específico**: `navigate_to_drogasil_page()` com tratamento customizado
- **Verificação de carregamento**: Aguarda título da página e delay adicional
- **Screenshots de navegação**: Captura em caso de erro na navegação

## Arquivos Modificados

### `config/settings.py`
```python
# Novos timeouts e configurações de screenshot
PLAYWRIGHT_TIMEOUT: int = 60000  # Aumentado de 30000
SCREENSHOTS_ENABLED: bool = True
SCREENSHOTS_DIR: str = "screenshots"
```

### `core/base_scraper.py`
- Adicionado método `take_error_screenshot()`
- Captura automática de screenshots no `run_scraping()`
- Criação automática da pasta de screenshots

### `services/drogasil_scraper.py`
- Importação de `PlaywrightTimeoutError`
- Método `navigate_to_drogasil_page()` específico
- Tratamento de timeout com seletores alternativos
- Screenshots em múltiplos pontos de falha
- Método `run_scraping()` customizado

## Como Usar

### Executar Teste
```bash
python test_drogasil_timeout.py
```

### Verificar Screenshots
Os screenshots são salvos automaticamente em `screenshots/` com nomes como:
- `drogasil_timeout_produtos_20241203_210530.png`
- `drogasil_navigation_timeout_20241203_210545.png`
- `drogasil_scraping_error_20241203_210600.png`

### Configurações
No arquivo `.env` ou diretamente no código:
```env
PLAYWRIGHT_TIMEOUT=60000
SCREENSHOTS_ENABLED=true
SCREENSHOTS_DIR=screenshots
```

## Tipos de Screenshots Capturados

1. **Navigation Timeout**: Erro ao navegar para a página
2. **Timeout Produtos**: Timeout ao aguardar produtos carregarem
3. **Timeout Fallback Failed**: Falha também com seletores alternativos
4. **Scraping Error**: Erro geral durante o scraping
5. **URL Error**: Erro ao processar uma URL específica
6. **Scraping Failed**: Falha geral do processo de scraping

## Benefícios

1. **Diagnóstico Visual**: Screenshots mostram exatamente o que estava na tela durante o erro
2. **Timeouts Maiores**: Mais tempo para sites lentos carregarem
3. **Fallback Inteligente**: Tentativa com seletores alternativos
4. **Logs Detalhados**: Informações específicas sobre cada tipo de erro
5. **Não Invasivo**: Sistema pode ser desabilitado via configuração

## Próximos Passos

1. Executar o teste para verificar se resolve os timeouts
2. Analisar os screenshots para entender melhor os problemas
3. Ajustar seletores se necessário baseado nos screenshots
4. Considerar implementar sistema similar para outros scrapers

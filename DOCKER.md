# 🐳 <PERSON><PERSON><PERSON> Docker - PriceNow

Este guia explica como executar o PriceNow usando Docker, com todas as dependências configuradas automaticamente.

## 🚀 In<PERSON>cio R<PERSON>pido

### 1. Pré-requisitos
- Docker (versão 20.10+)
- Docker Compose (versão 2.0+)
- Git

### 2. <PERSON><PERSON> e Execute
```bash
# Clone o repositório
git clone <repository-url>
cd pricenow

# Copie o arquivo de configuração
cp .env.example .env

# Inicie todos os serviços
docker-compose up -d

# Aguarde alguns minutos para tudo inicializar
# Verifique o status
docker-compose ps
```

### 3. Acesse os Serviços
- **API**: http://localhost:8000
- **Documentação**: http://localhost:8000/docs
- **PgAdmin**: http://localhost:8080 (<EMAIL> / admin)

## 📋 Comandos Úteis

### Usando Make (Recomendado)
```bash
# Ver todos os comandos disponíveis
make help

# Configuração para desenvolvimento
make dev-setup

# Configuração para produção
make prod-setup

# Construir imagens
make build

# Iniciar serviços
make up

# Parar serviços
make down

# Ver logs
make logs
make logs-api
make logs-scraper

# Executar scrapers
make run-scraper
make run-scraper-ml

# Backup do banco
make backup

# Limpar sistema
make clean
```

### Usando Docker Compose Diretamente
```bash
# Construir imagens
docker-compose build

# Iniciar serviços
docker-compose up -d

# Ver logs
docker-compose logs -f

# Parar serviços
docker-compose down

# Ver status
docker-compose ps
```

## 🏗️ Arquitetura dos Containers

### Serviços Principais

#### 1. **API Container** (`pricenow_api`)
- **Imagem**: Construída a partir do `Dockerfile`
- **Porta**: 8000
- **Função**: Serve a API REST FastAPI
- **Dependências**: PostgreSQL, Redis

#### 2. **Scraper Container** (`pricenow_scraper`)
- **Imagem**: Construída a partir do `Dockerfile.scraper`
- **Função**: Executa scrapers automaticamente
- **Dependências**: PostgreSQL, API
- **Browsers**: Chromium, Firefox, WebKit instalados

#### 3. **PostgreSQL** (`pricenow_postgres`)
- **Imagem**: postgres:15
- **Porta**: 5432
- **Volume**: `postgres_data`
- **Função**: Banco de dados principal

#### 4. **Redis** (`pricenow_redis`)
- **Imagem**: redis:7-alpine
- **Porta**: 6379
- **Volume**: `redis_data`
- **Função**: Cache e sessões

#### 5. **PgAdmin** (`pricenow_pgadmin`)
- **Imagem**: dpage/pgadmin4
- **Porta**: 8080
- **Volume**: `pgadmin_data`
- **Função**: Interface web para PostgreSQL

## 🔧 Configurações

### Variáveis de Ambiente
Edite o arquivo `.env`:

```env
# Banco de dados
DATABASE_URL=********************************************/pricenow

# API
API_HOST=0.0.0.0
API_PORT=8000

# Playwright
PLAYWRIGHT_HEADLESS=true
PLAYWRIGHT_TIMEOUT=30000

# Scraping
SCRAPING_DELAY=2.0
MAX_RETRIES=3

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/scraper.log
```

### Volumes Persistentes
- `postgres_data`: Dados do PostgreSQL
- `redis_data`: Dados do Redis
- `pgadmin_data`: Configurações do PgAdmin
- `./logs`: Logs da aplicação (mapeado do host)

## 🌍 Ambientes

### Desenvolvimento (`docker-compose.dev.yml`)
```bash
# Apenas banco de dados e PgAdmin
docker-compose -f docker-compose.dev.yml up -d

# Para desenvolvimento local da aplicação
make up-dev
```

### Produção (`docker-compose.prod.yml`)
```bash
# Ambiente completo com Nginx e backup
docker-compose -f docker-compose.prod.yml up -d

# Ou usando make
make prod-setup
```

## 🔍 Monitoramento

### Health Checks
Todos os serviços têm health checks configurados:
```bash
# Verificar saúde dos containers
docker-compose ps

# Health check da API
curl http://localhost:8000/health
```

### Logs
```bash
# Logs em tempo real
docker-compose logs -f

# Logs específicos
docker-compose logs -f api
docker-compose logs -f scraper
docker-compose logs -f postgres

# Logs com make
make logs
make logs-api
make logs-scraper
```

### Recursos
```bash
# Monitorar uso de recursos
docker stats

# Ou com make
make monitor
```

## 🛠️ Desenvolvimento

### Executar Comandos nos Containers
```bash
# Shell da API
docker exec -it pricenow_api bash
make shell-api

# Shell do scraper
docker exec -it pricenow_scraper bash
make shell-scraper

# PostgreSQL
docker exec -it pricenow_postgres psql -U postgres -d pricenow
make shell-postgres
```

### Executar Scrapers Manualmente
```bash
# Todos os scrapers
docker exec pricenow_scraper python main.py scrape --all
make run-scraper

# Scraper específico
docker exec pricenow_scraper python main.py scrape --scraper mercadolivre
make run-scraper-ml

# Listar scrapers
docker exec pricenow_scraper python main.py list-scrapers
```

### Rebuild e Update
```bash
# Reconstruir imagens
docker-compose build --no-cache

# Atualizar aplicação
make update
```

## 💾 Backup e Restore

### Backup Automático
O ambiente de produção inclui backup automático diário:
```bash
# Backup manual
make backup

# Ou diretamente
docker exec pricenow_postgres pg_dump -U postgres pricenow > backup.sql
```

### Restore
```bash
# Restaurar backup
make restore FILE=backup_20231201_120000.sql

# Ou diretamente
docker exec -i pricenow_postgres psql -U postgres -d pricenow < backup.sql
```

## 🚨 Troubleshooting

### Container não inicia
```bash
# Verificar logs
docker-compose logs [service_name]

# Verificar recursos
docker system df
docker system prune
```

### Banco de dados não conecta
```bash
# Verificar se PostgreSQL está rodando
docker-compose ps postgres

# Testar conexão
docker exec pricenow_postgres pg_isready -U postgres

# Verificar logs do banco
docker-compose logs postgres
```

### Scraper falha
```bash
# Verificar logs do scraper
docker-compose logs scraper

# Executar manualmente para debug
docker exec -it pricenow_scraper python main.py scrape --scraper example
```

### Problemas de permissão
```bash
# Verificar permissões dos volumes
ls -la logs/

# Corrigir permissões se necessário
sudo chown -R $USER:$USER logs/
```

### Limpar sistema
```bash
# Limpar containers e volumes não utilizados
make clean

# Limpar tudo (CUIDADO: apaga dados!)
make clean-all
```

## 🔒 Segurança

### Produção
- Altere senhas padrão no `.env`
- Configure SSL/TLS no Nginx
- Use secrets do Docker Swarm se aplicável
- Configure firewall adequadamente

### Rede
- Containers se comunicam via rede interna
- Apenas portas necessárias são expostas
- Rate limiting configurado no Nginx

## 📊 Performance

### Recursos Recomendados
- **Mínimo**: 2GB RAM, 2 CPU cores
- **Recomendado**: 4GB RAM, 4 CPU cores
- **Produção**: 8GB RAM, 8 CPU cores

### Otimizações
- Ajuste `SCRAPING_DELAY` conforme necessário
- Configure limites de recursos no docker-compose
- Use SSD para volumes de banco de dados

## 🔄 CI/CD

### GitHub Actions (exemplo)
```yaml
name: Deploy
on:
  push:
    branches: [main]
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Deploy
        run: |
          docker-compose -f docker-compose.prod.yml pull
          docker-compose -f docker-compose.prod.yml up -d
```

### Atualizações
```bash
# Atualização sem downtime
make update

# Ou manual
git pull
docker-compose build
docker-compose up -d --no-deps api scraper
```

Para mais informações, consulte o `README.md` principal.

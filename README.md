# PriceNow - Sistema de Web Scraping de Preços

Sistema completo de web scraping para monitoramento de preços com Python, Playwright, banco de dados PostgreSQL e API REST.

## 🚀 Características

- **Web Scraping Modular**: Sistema baseado em services para scrapers individualizados
- **Playwright**: Automação de browser moderna e robusta
- **API REST**: FastAPI para disponibilizar os dados coletados
- **Banco de Dados**: PostgreSQL com SQLAlchemy ORM
- **Logging**: Sistema de logs estruturado com Loguru
- **Configuração**: Gerenciamento de configurações com Pydantic
- **Docker**: Containerização para fácil deploy

## 📁 Estrutura do Projeto

```
pricenow/
├── api/                    # API FastAPI
│   ├── __init__.py
│   ├── main.py            # Aplicação principal da API
│   └── schemas.py         # Schemas Pydantic
├── config/                # Configurações
│   ├── __init__.py
│   └── settings.py        # Configurações do sistema
├── core/                  # Classes base
│   ├── __init__.py
│   └── base_scraper.py    # Classe base para scrapers
├── database/              # Banco de dados
│   ├── __init__.py
│   ├── connection.py      # Conexões e sessões
│   └── models.py          # Modelos SQLAlchemy
├── services/              # Scrapers individuais
│   ├── __init__.py
│   └── example_scraper.py # Exemplo de scraper
├── utils/                 # Utilitários
│   ├── __init__.py
│   └── logger.py          # Sistema de logging
├── main.py               # Ponto de entrada principal
├── requirements.txt      # Dependências Python
├── docker-compose.yml    # Configuração Docker
├── .env.example         # Exemplo de variáveis de ambiente
└── README.md            # Este arquivo
```

## 🛠️ Instalação

### 🐳 Opção 1: Docker (Recomendado - Zero Problemas de Dependências)

**Início ultra-rápido com todas as dependências pré-configuradas e compatíveis:**

```bash
# 1. Clone o repositório
git clone <repository-url>
cd pricenow

# 2. Configure e inicie
cp .env.example .env
docker-compose up -d

# 3. Aguarde 2-3 minutos e acesse:
# API: http://localhost:8000
# Docs: http://localhost:8000/docs
# PgAdmin: http://localhost:8080
```

**Executar scrapers:**
```bash
docker exec pricenow_scraper python main.py scrape --scraper mercadolivre
docker exec pricenow_scraper python main.py scrape --all
```

📖 **Guia completo**: [README-DOCKER.md](README-DOCKER.md)

> ⚠️ **Problemas de dependências?** Consulte: [DEPENDENCIES-FIX.md](DEPENDENCIES-FIX.md)

---

### 💻 Opção 2: Instalação Local (Dependências Corrigidas)

#### 1. Clone o repositório
```bash
git clone <repository-url>
cd pricenow
```

#### 2. Crie um ambiente virtual
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# ou
venv\Scripts\activate     # Windows
```

#### 3. Instale as dependências
```bash
pip install -r requirements.txt
```

#### 4. Instale o Playwright
```bash
playwright install
```

#### 5. Configure as variáveis de ambiente
```bash
cp .env.example .env
# Edite o arquivo .env conforme necessário
```

#### 6. Inicie o banco de dados (Docker)
```bash
docker-compose -f docker-compose.dev.yml up -d
```

#### 7. Inicialize o sistema
```bash
python main.py init
```

## 🚀 Uso

### Comandos Disponíveis

#### Inicializar o sistema
```bash
python main.py init
```

#### Listar scrapers disponíveis
```bash
python main.py list-scrapers
```

#### Executar um scraper específico
```bash
python main.py scrape --scraper example
```

#### Executar todos os scrapers
```bash
python main.py scrape --all
```

#### Iniciar a API
```bash
python main.py api
```

### API Endpoints

A API estará disponível em `http://localhost:8000`

#### Principais endpoints:

- `GET /` - Informações da API
- `GET /health` - Health check
- `GET /products` - Lista produtos com filtros
- `GET /products/{id}` - Produto específico
- `GET /sites` - Lista sites disponíveis
- `GET /categories` - Lista categorias
- `GET /brands` - Lista marcas
- `GET /jobs` - Lista jobs de scraping
- `GET /stats` - Estatísticas do sistema

#### Documentação interativa:
- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

## 🔧 Criando um Novo Scraper

### 1. Crie um novo arquivo em `services/`

```python
# services/meu_site_scraper.py
from typing import List, Dict, Any
from core.base_scraper import BaseScraper

class MeuSiteScraper(BaseScraper):
    def __init__(self):
        super().__init__("meu_site")
        self.base_url = "https://meusite.com"
    
    def get_urls_to_scrape(self) -> List[str]:
        return [
            f"{self.base_url}/categoria1",
            f"{self.base_url}/categoria2",
        ]
    
    async def scrape(self) -> List[Dict[str, Any]]:
        products = []
        
        # Aguarda elementos carregarem
        await self.page.wait_for_selector('.produto')
        
        # Extrai produtos
        elements = await self.page.query_selector_all('.produto')
        
        for element in elements:
            # Extrai dados do produto
            name = await element.query_selector('.nome')
            price = await element.query_selector('.preco')
            
            if name and price:
                products.append({
                    'product_name': await name.inner_text(),
                    'price': self.extract_price(await price.inner_text()),
                    'url': self.page.url,
                    # ... outros campos
                })
        
        return products
```

### 2. Registre o scraper em `main.py`

```python
# Em main.py, no método register_scrapers()
self.scrapers['meu_site'] = MeuSiteScraper
```

## 🐳 Docker

### 🚀 Execução Completa com Docker

**Todos os serviços pré-configurados:**
```bash
# Iniciar aplicação completa
docker-compose up -d

# Ver status
docker-compose ps

# Ver logs
docker-compose logs -f
```

### 📦 Containers Incluídos:
- **API FastAPI**: `localhost:8000`
- **Scraper Automático**: Executa coletas periodicamente
- **PostgreSQL**: `localhost:5432`
- **Redis**: `localhost:6379`
- **PgAdmin**: `http://localhost:8080`

### 🕷️ Executar Scrapers:
```bash
# Mercado Livre
docker exec pricenow_scraper python main.py scrape --scraper mercadolivre

# Americanas
docker exec pricenow_scraper python main.py scrape --scraper americanas

# Todos os scrapers
docker exec pricenow_scraper python main.py scrape --all
```

### 🔧 Comandos Úteis:
```bash
# Parar serviços
docker-compose down

# Reconstruir imagens
docker-compose build

# Backup do banco
docker exec pricenow_postgres pg_dump -U postgres pricenow > backup.sql

# Logs específicos
docker-compose logs -f api
docker-compose logs -f scraper
```

📖 **Guia completo Docker**: [README-DOCKER.md](README-DOCKER.md)

## 📊 Monitoramento

### Logs
Os logs são salvos em `logs/scraper.log` e também exibidos no console.

### PgAdmin
Acesse `http://localhost:8080` para gerenciar o banco de dados:
- Email: `<EMAIL>`
- Senha: `admin`

## ⚙️ Configurações

As configurações podem ser ajustadas através de variáveis de ambiente ou no arquivo `.env`:

```env
# Banco de dados
DATABASE_URL=postgresql://postgres:password@localhost:5432/pricenow

# API
API_HOST=0.0.0.0
API_PORT=8000

# Playwright
PLAYWRIGHT_HEADLESS=true
PLAYWRIGHT_TIMEOUT=30000

# Scraping
SCRAPING_DELAY=1.0
MAX_RETRIES=3

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/scraper.log
```

## 🤝 Contribuindo

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📝 Licença

Este projeto está sob a licença MIT. Veja o arquivo `LICENSE` para mais detalhes.

## 🆘 Suporte

Se você encontrar algum problema ou tiver dúvidas:

1. Verifique os logs em `logs/scraper.log`
2. Consulte a documentação da API em `/docs`
3. Abra uma issue no repositório

## 🔮 Próximas Funcionalidades

- [ ] Sistema de agendamento de scrapers
- [ ] Interface web para gerenciamento
- [ ] Notificações de mudanças de preço
- [ ] Cache com Redis
- [ ] Métricas e dashboards
- [ ] Suporte a proxies
- [ ] Detecção de captcha

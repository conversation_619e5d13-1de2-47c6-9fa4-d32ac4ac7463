#!/bin/bash

# Script de inicialização rápida do PriceNow
set -e

echo "🚀 Iniciando PriceNow com Docker..."
echo "=================================="

# Verificar se Docker está instalado
if ! command -v docker &> /dev/null; then
    echo "❌ Docker não encontrado. Instale o Docker primeiro."
    echo "   https://docs.docker.com/get-docker/"
    exit 1
fi

# Verificar se Docker Compose está instalado
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose não encontrado. Instale o Docker Compose primeiro."
    echo "   https://docs.docker.com/compose/install/"
    exit 1
fi

echo "✅ Docker e Docker Compose encontrados"

# Verificar se arquivo .env existe
if [ ! -f .env ]; then
    echo "📝 Criando arquivo .env..."
    cp .env.example .env
    echo "✅ Arquivo .env criado"
    echo "💡 Edite o arquivo .env se necessário"
else
    echo "✅ Arquivo .env já existe"
fi

# Criar diretório de logs
mkdir -p logs
echo "✅ Diretório de logs criado"

# Criar diretório de backups
mkdir -p backups
echo "✅ Diretório de backups criado"

echo ""
echo "🏗️  Construindo imagens Docker..."
docker-compose build

echo ""
echo "🚀 Iniciando serviços..."
docker-compose up -d

echo ""
echo "⏳ Aguardando serviços iniciarem..."
sleep 30

# Verificar se os serviços estão rodando
echo ""
echo "🔍 Verificando status dos serviços..."
docker-compose ps

# Inicializar banco de dados
echo ""
echo "🗄️  Inicializando banco de dados..."
docker exec pricenow_api python main.py init || echo "⚠️  Banco já inicializado"

echo ""
echo "🎉 PriceNow iniciado com sucesso!"
echo "=================================="
echo ""
echo "📋 Serviços disponíveis:"
echo "   🌐 API: http://localhost:8000"
echo "   📚 Documentação: http://localhost:8000/docs"
echo "   🗄️  PgAdmin: http://localhost:8080"
echo "      Email: <EMAIL>"
echo "      Senha: admin"
echo ""
echo "🔧 Comandos úteis:"
echo "   docker-compose logs -f          # Ver logs"
echo "   docker-compose ps               # Status dos containers"
echo "   docker-compose down             # Parar serviços"
echo "   make help                       # Ver todos os comandos (se make instalado)"
echo ""
echo "🕷️  Para executar scrapers:"
echo "   docker exec pricenow_scraper python main.py scrape --scraper mercadolivre"
echo "   docker exec pricenow_scraper python main.py scrape --all"
echo ""
echo "📊 Para monitorar:"
echo "   docker-compose logs -f api      # Logs da API"
echo "   docker-compose logs -f scraper  # Logs do scraper"
echo "   docker stats                    # Uso de recursos"
echo ""

# Verificar se a API está respondendo
echo "🔍 Testando API..."
sleep 5
if curl -s http://localhost:8000/health > /dev/null; then
    echo "✅ API está respondendo!"
else
    echo "⚠️  API ainda não está respondendo. Aguarde mais alguns segundos."
fi

echo ""
echo "🎯 Próximos passos:"
echo "   1. Acesse http://localhost:8000/docs para ver a documentação da API"
echo "   2. Execute scrapers para coletar dados"
echo "   3. Use a API para consultar os dados coletados"
echo ""
echo "Para parar os serviços: docker-compose down"
echo "Para ver este guia novamente: ./start.sh --help"

# Docker Compose para produção
version: '3.8'

services:
  # Banco de dados PostgreSQL
  postgres:
    image: postgres:15
    container_name: pricenow_postgres_prod
    environment:
      POSTGRES_DB: pricenow
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-password}
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_prod_data:/var/lib/postgresql/data
      - ./backups:/backups
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Cache Redis
  redis:
    image: redis:7-alpine
    container_name: pricenow_redis_prod
    ports:
      - "6379:6379"
    volumes:
      - redis_prod_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # API da aplicação
  api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: pricenow_api_prod
    environment:
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD:-password}@postgres:5432/pricenow
      - API_HOST=0.0.0.0
      - API_PORT=8000
      - PLAYWRIGHT_HEADLESS=true
      - LOG_LEVEL=INFO
    ports:
      - "8000:8000"
    volumes:
      - ./logs:/app/logs
      - ./.env:/app/.env:ro
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  # Scraper de dados
  scraper:
    build:
      context: .
      dockerfile: Dockerfile.scraper
    container_name: pricenow_scraper_prod
    environment:
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD:-password}@postgres:5432/pricenow
      - PLAYWRIGHT_HEADLESS=true
      - SCRAPING_DELAY=3.0
      - MAX_RETRIES=5
      - LOG_LEVEL=INFO
    volumes:
      - ./logs:/app/logs
      - ./.env:/app/.env:ro
    depends_on:
      postgres:
        condition: service_healthy
      api:
        condition: service_healthy
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G

  # Nginx como proxy reverso
  nginx:
    image: nginx:alpine
    container_name: pricenow_nginx_prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - api
    restart: unless-stopped

  # Backup automático
  backup:
    image: postgres:15
    container_name: pricenow_backup_prod
    environment:
      - PGPASSWORD=${POSTGRES_PASSWORD:-password}
    volumes:
      - ./backups:/backups
      - ./backup-script.sh:/backup-script.sh:ro
    depends_on:
      - postgres
    restart: unless-stopped
    command: >
      sh -c "
        while true; do
          sleep 86400;
          /backup-script.sh;
        done
      "

volumes:
  postgres_prod_data:
  redis_prod_data:

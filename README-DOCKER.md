# 🐳 PriceNow - Execução com Docker

## 🚀 Início Ultra-Rápido

### 1. Pré-requisitos
- Docker Desktop instalado
- Git (para clonar o repositório)

### 2. Executar em 3 comandos
```bash
# 1. Clone o repositório
git clone <repository-url>
cd pricenow

# 2. Configure e inicie (Windows)
copy .env.example .env
docker-compose up -d

# 2. Configure e inicie (Linux/Mac)
cp .env.example .env
docker-compose up -d
```

### 3. Aguarde e acesse
- Aguarde 2-3 minutos para tudo inicializar
- **API**: http://localhost:8000
- **Documentação**: http://localhost:8000/docs
- **PgAdmin**: http://localhost:8080

## 📦 O que está incluído

### Containers Automáticos:
- ✅ **API FastAPI** - Porta 8000
- ✅ **PostgreSQL** - Banco de dados
- ✅ **Redis** - Cache
- ✅ **PgAdmin** - Interface web do banco
- ✅ **Scraper** - Coleta automática de dados
- ✅ **Playwright** - Browsers pré-instalados

### Dependências Pré-configuradas:
- ✅ Python 3.11
- ✅ Playwright + Chromium/Firefox/WebKit
- ✅ PostgreSQL 15
- ✅ Redis 7
- ✅ Todas as bibliotecas Python

## 🎯 Comandos Essenciais

### Gerenciar Serviços
```bash
# Iniciar tudo
docker-compose up -d

# Ver status
docker-compose ps

# Ver logs
docker-compose logs -f

# Parar tudo
docker-compose down

# Reiniciar
docker-compose restart
```

### Executar Scrapers
```bash
# Listar scrapers disponíveis
docker exec pricenow_scraper python main.py list-scrapers

# Executar Mercado Livre
docker exec pricenow_scraper python main.py scrape --scraper mercadolivre

# Executar Americanas
docker exec pricenow_scraper python main.py scrape --scraper americanas

# Executar todos
docker exec pricenow_scraper python main.py scrape --all
```

### Acessar Containers
```bash
# Shell da API
docker exec -it pricenow_api bash

# Shell do scraper
docker exec -it pricenow_scraper bash

# PostgreSQL
docker exec -it pricenow_postgres psql -U postgres -d pricenow
```

## 🔧 Configuração

### Arquivo .env
```env
# Banco de dados (já configurado para Docker)
DATABASE_URL=********************************************/pricenow

# API
API_HOST=0.0.0.0
API_PORT=8000

# Playwright
PLAYWRIGHT_HEADLESS=true
PLAYWRIGHT_TIMEOUT=30000

# Scraping
SCRAPING_DELAY=2.0
MAX_RETRIES=3

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/scraper.log
```

### Portas Utilizadas
- **8000**: API FastAPI
- **5432**: PostgreSQL
- **6379**: Redis
- **8080**: PgAdmin

## 📊 Monitoramento

### Logs em Tempo Real
```bash
# Todos os serviços
docker-compose logs -f

# Apenas API
docker-compose logs -f api

# Apenas scraper
docker-compose logs -f scraper

# Apenas banco
docker-compose logs -f postgres
```

### Status e Recursos
```bash
# Status dos containers
docker-compose ps

# Uso de recursos
docker stats

# Espaço em disco
docker system df
```

## 🗄️ Banco de Dados

### PgAdmin (Interface Web)
1. Acesse: http://localhost:8080
2. Login: `<EMAIL>`
3. Senha: `admin`
4. Adicione servidor:
   - Host: `postgres`
   - Port: `5432`
   - Database: `pricenow`
   - Username: `postgres`
   - Password: `password`

### Linha de Comando
```bash
# Acessar PostgreSQL
docker exec -it pricenow_postgres psql -U postgres -d pricenow

# Comandos SQL úteis
\dt                    # Listar tabelas
SELECT * FROM scraped_data LIMIT 10;
SELECT COUNT(*) FROM scraped_data;
```

## 💾 Backup e Restore

### Backup
```bash
# Backup manual
docker exec pricenow_postgres pg_dump -U postgres pricenow > backup.sql

# Backup com data
docker exec pricenow_postgres pg_dump -U postgres pricenow > backup_$(date +%Y%m%d).sql
```

### Restore
```bash
# Restaurar backup
docker exec -i pricenow_postgres psql -U postgres -d pricenow < backup.sql
```

## 🚨 Solução de Problemas

### Container não inicia
```bash
# Ver logs de erro
docker-compose logs [nome_do_servico]

# Exemplo
docker-compose logs postgres
docker-compose logs api
```

### Porta já em uso
```bash
# Verificar o que está usando a porta
netstat -tulpn | grep :8000

# Ou parar outros serviços
docker-compose down
```

### Banco não conecta
```bash
# Verificar se PostgreSQL está rodando
docker-compose ps postgres

# Testar conexão
docker exec pricenow_postgres pg_isready -U postgres
```

### Scraper falha
```bash
# Ver logs detalhados
docker-compose logs scraper

# Executar manualmente para debug
docker exec -it pricenow_scraper python main.py scrape --scraper example
```

### Limpar tudo e recomeçar
```bash
# Parar e remover containers
docker-compose down -v

# Remover imagens (opcional)
docker-compose down -v --rmi all

# Limpar sistema Docker
docker system prune -f

# Reconstruir tudo
docker-compose build --no-cache
docker-compose up -d
```

## 🔄 Atualizações

### Atualizar código
```bash
# Baixar atualizações
git pull

# Reconstruir e reiniciar
docker-compose build
docker-compose up -d
```

### Atualizar dependências
```bash
# Reconstruir sem cache
docker-compose build --no-cache
```

## 🌍 Ambientes

### Desenvolvimento (apenas banco)
```bash
# Usar arquivo específico
docker-compose -f docker-compose.dev.yml up -d

# Executar aplicação localmente
python main.py api
```

### Produção
```bash
# Usar arquivo de produção
docker-compose -f docker-compose.prod.yml up -d
```

## 📈 Performance

### Recursos Recomendados
- **Mínimo**: 4GB RAM, 2 CPU cores
- **Recomendado**: 8GB RAM, 4 CPU cores
- **Produção**: 16GB RAM, 8 CPU cores

### Otimizações
```bash
# Ajustar recursos no docker-compose.yml
services:
  api:
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M
```

## 🎯 Próximos Passos

1. **Teste a API**: http://localhost:8000/docs
2. **Execute scrapers**: `docker exec pricenow_scraper python main.py scrape --all`
3. **Monitore dados**: PgAdmin em http://localhost:8080
4. **Personalize scrapers**: Edite arquivos em `services/`
5. **Configure agendamentos**: Use `utils/scheduler.py`

## 📞 Suporte

### Comandos de Debug
```bash
# Informações do sistema
docker version
docker-compose version

# Logs completos
docker-compose logs --no-color > debug.log

# Status detalhado
docker-compose ps
docker stats --no-stream
```

### Verificação de Saúde
```bash
# Health check da API
curl http://localhost:8000/health

# Teste de conectividade
docker exec pricenow_api python -c "from database.connection import DatabaseManager; print('DB OK')"
```

Para mais detalhes, consulte `DOCKER.md` e `README.md`.

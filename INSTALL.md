# Guia de Instalação - PriceNow

## 🚀 Instalação Rápida

### Opção 1: Script Automático (Recomendado)
```bash
python setup.py
```

### Opção 2: Instalação Manual

#### 1. Pré-requisitos
- Python 3.8 ou superior
- pip (gerenciador de pacotes Python)
- <PERSON>er (opcional, para banco de dados)

#### 2. Clone e configure o ambiente
```bash
# Clone o repositório
git clone <repository-url>
cd pricenow

# Crie ambiente virtual
python -m venv venv

# Ative o ambiente virtual
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate

# Instale dependências
pip install -r requirements.txt

# Instale browsers do Playwright
playwright install
```

#### 3. Configure variáveis de ambiente
```bash
# Copie o arquivo de exemplo
cp .env.example .env

# Edite o arquivo .env conforme necessário
```

#### 4. Configure o banco de dados

**Opção A: Docker (Recomendado)**
```bash
# Inicie PostgreSQL com Docker
docker-compose up -d postgres

# Acesse PgAdmin em http://localhost:8080
# Email: <EMAIL>
# Senha: admin
```

**Opção B: PostgreSQL Local**
```bash
# Instale PostgreSQL localmente
# Crie banco de dados 'pricenow'
# Configure DATABASE_URL no .env
```

#### 5. Inicialize o sistema
```bash
python main.py init
```

## 🎯 Primeiros Passos

### 1. Teste o sistema
```bash
# Liste scrapers disponíveis
python main.py list-scrapers

# Execute um scraper de teste
python main.py scrape --scraper example
```

### 2. Execute scrapers reais
```bash
# Mercado Livre
python main.py scrape --scraper mercadolivre

# Americanas
python main.py scrape --scraper americanas

# Todos os scrapers
python main.py scrape --all
```

### 3. Inicie a API
```bash
python main.py api
```

Acesse:
- API: http://localhost:8000
- Documentação: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 🔧 Configuração Avançada

### Variáveis de Ambiente (.env)
```env
# Banco de dados
DATABASE_URL=postgresql://postgres:password@localhost:5432/pricenow

# API
API_HOST=0.0.0.0
API_PORT=8000

# Playwright
PLAYWRIGHT_HEADLESS=true
PLAYWRIGHT_TIMEOUT=30000

# Scraping
SCRAPING_DELAY=1.0
MAX_RETRIES=3

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/scraper.log
```

### Configuração de Scrapers
Edite `config/scrapers.py` para personalizar:
- Seletores CSS
- Headers HTTP
- Delays entre requisições
- URLs e categorias

## 🐳 Docker

### Serviços Disponíveis
```bash
# Inicie todos os serviços
docker-compose up -d

# Apenas PostgreSQL
docker-compose up -d postgres

# Apenas Redis
docker-compose up -d redis

# PgAdmin
docker-compose up -d pgadmin
```

### Portas
- PostgreSQL: 5432
- Redis: 6379
- PgAdmin: 8080

## 🔍 Verificação da Instalação

### 1. Teste de conectividade
```bash
# Teste conexão com banco
python -c "from database.connection import DatabaseManager; DatabaseManager.init_db(); print('✅ Banco OK')"

# Teste Playwright
python -c "import asyncio; from playwright.async_api import async_playwright; print('✅ Playwright OK')"
```

### 2. Logs
```bash
# Verifique logs
tail -f logs/scraper.log
```

### 3. API Health Check
```bash
curl http://localhost:8000/health
```

## 🚨 Solução de Problemas

### Erro: "playwright not found"
```bash
# Reinstale Playwright
pip install playwright
playwright install
```

### Erro: "psycopg2 installation failed"
```bash
# Ubuntu/Debian
sudo apt-get install libpq-dev python3-dev

# CentOS/RHEL
sudo yum install postgresql-devel python3-devel

# Windows
# Use psycopg2-binary (já incluído no requirements.txt)
```

### Erro: "Permission denied" (Linux/Mac)
```bash
# Torne o script executável
chmod +x setup.py
chmod +x main.py
```

### Banco de dados não conecta
1. Verifique se PostgreSQL está rodando
2. Confirme credenciais no .env
3. Teste conexão manual:
```bash
psql -h localhost -U postgres -d pricenow
```

### Scrapers não funcionam
1. Verifique conexão com internet
2. Sites podem ter mudado estrutura
3. Ajuste seletores em `config/scrapers.py`
4. Verifique logs para erros específicos

## 📊 Monitoramento

### Logs
- Arquivo: `logs/scraper.log`
- Rotação automática (10MB)
- Retenção: 30 dias

### Métricas
- Jobs de scraping: tabela `scraping_jobs`
- Produtos coletados: tabela `scraped_data`
- Estatísticas: endpoint `/stats`

### PgAdmin
- URL: http://localhost:8080
- Email: <EMAIL>
- Senha: admin

## 🔄 Atualizações

### Atualizar dependências
```bash
pip install -r requirements.txt --upgrade
```

### Atualizar browsers
```bash
playwright install
```

### Backup do banco
```bash
# Docker
docker exec pricenow_postgres pg_dump -U postgres pricenow > backup.sql

# Local
pg_dump -U postgres pricenow > backup.sql
```

## 🆘 Suporte

### Logs de Debug
```bash
# Ative logs detalhados
export LOG_LEVEL=DEBUG
python main.py scrape --scraper mercadolivre
```

### Teste Individual
```bash
# Teste scraper específico
python -c "
import asyncio
from services.mercadolivre_scraper import MercadoLivreScraper

async def test():
    scraper = MercadoLivreScraper()
    await scraper.run_scraping()

asyncio.run(test())
"
```

### Verificação de Dependências
```bash
pip check
pip list --outdated
```

## 📝 Próximos Passos

1. **Personalize scrapers** em `services/`
2. **Configure agendamentos** com `utils/scheduler.py`
3. **Monitore performance** via API `/stats`
4. **Implemente novos sites** seguindo `services/example_scraper.py`
5. **Configure alertas** para mudanças de preço

Para mais informações, consulte o `README.md` principal.

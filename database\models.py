from sqlalchemy import Column, Integer, String, Float, DateTime, Text, Boolean, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from datetime import datetime

Base = declarative_base()

class ScrapedData(Base):
    __tablename__ = "scraped_data"
    
    id = Column(Integer, primary_key=True, index=True)
    site_name = Column(String(100), nullable=False, index=True)
    product_name = Column(String(500), nullable=False)
    price = Column(Float, nullable=True)
    original_price = Column(Float, nullable=True)
    discount_percentage = Column(Float, nullable=True)
    url = Column(Text, nullable=False)
    image_url = Column(Text, nullable=True)
    description = Column(Text, nullable=True)
    category = Column(String(200), nullable=True)
    brand = Column(String(200), nullable=True)
    availability = Column(Boolean, default=True)
    rating = Column(Float, nullable=True)
    reviews_count = Column(Integer, nullable=True)
    additional_data = Column(JSON, nullable=True)  # Para dados específicos do site
    scraped_at = Column(DateTime(timezone=True), server_default=func.now())
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

class ScrapingJob(Base):
    __tablename__ = "scraping_jobs"
    
    id = Column(Integer, primary_key=True, index=True)
    site_name = Column(String(100), nullable=False)
    status = Column(String(50), default="pending")  # pending, running, completed, failed
    start_time = Column(DateTime(timezone=True), nullable=True)
    end_time = Column(DateTime(timezone=True), nullable=True)
    items_scraped = Column(Integer, default=0)
    errors_count = Column(Integer, default=0)
    error_message = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

class SiteConfig(Base):
    __tablename__ = "site_configs"
    
    id = Column(Integer, primary_key=True, index=True)
    site_name = Column(String(100), unique=True, nullable=False)
    base_url = Column(String(500), nullable=False)
    is_active = Column(Boolean, default=True)
    scraping_interval = Column(Integer, default=3600)  # em segundos
    selectors = Column(JSON, nullable=False)  # Seletores CSS/XPath
    headers = Column(JSON, nullable=True)  # Headers customizados
    cookies = Column(JSON, nullable=True)  # Cookies necessários
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

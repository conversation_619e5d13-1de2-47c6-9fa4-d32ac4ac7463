#!/usr/bin/env python3
"""
PriceNow - Sistema de Web Scraping de Preços
Ponto de entrada principal da aplicação
"""

import asyncio
import argparse
import sys
from typing import List
from utils.logger import get_logger
from database.connection import DatabaseManager
from services.example_scraper import ExampleScraper
from services.mercadolivre_scraper import MercadoLivreScraper
from services.drogasil_scraper import DrogasilScraper
from config.settings import settings

logger = get_logger("main")

class ScrapingManager:
    """Gerenciador principal do sistema de scraping"""
    
    def __init__(self):
        self.scrapers = {}
        self.register_scrapers()
    
    def register_scrapers(self):
        """Registra todos os scrapers disponíveis"""
        # Registra o scraper de exemplo
        self.scrapers['example'] = ExampleScraper

        # Registra scrapers reais
        self.scrapers['mercadolivre'] = MercadoLivreScraper
        self.scrapers['drogasil'] = DrogasilScraper

        # Aqui você pode adicionar outros scrapers:
        # self.scrapers['amazon'] = AmazonScraper
        # self.scrapers['americanas'] = AmericanasScraper

        logger.info(f"Scrapers registrados: {list(self.scrapers.keys())}")
    
    def get_available_scrapers(self) -> List[str]:
        """Retorna lista de scrapers disponíveis"""
        return list(self.scrapers.keys())
    
    async def run_scraper(self, scraper_name: str):
        """Executa um scraper específico"""
        if scraper_name not in self.scrapers:
            raise ValueError(f"Scraper '{scraper_name}' não encontrado")
        
        scraper_class = self.scrapers[scraper_name]
        scraper = scraper_class()
        
        logger.info(f"Iniciando scraper: {scraper_name}")
        await scraper.run_scraping()
        logger.info(f"Scraper {scraper_name} concluído")
    
    async def run_all_scrapers(self):
        """Executa todos os scrapers registrados"""
        logger.info("Iniciando execução de todos os scrapers")
        
        for scraper_name in self.scrapers:
            try:
                await self.run_scraper(scraper_name)
            except Exception as e:
                logger.error(f"Erro ao executar scraper {scraper_name}: {e}")
        
        logger.info("Execução de todos os scrapers concluída")

def init_database():
    """Inicializa o banco de dados"""
    try:
        logger.info("Inicializando banco de dados...")
        DatabaseManager.init_db()
        logger.info("Banco de dados inicializado com sucesso")
    except Exception as e:
        logger.error(f"Erro ao inicializar banco de dados: {e}")
        sys.exit(1)

async def run_api():
    """Executa a API"""
    import uvicorn
    from api.main import app
    
    logger.info(f"Iniciando API em {settings.API_HOST}:{settings.API_PORT}")
    
    config = uvicorn.Config(
        app,
        host=settings.API_HOST,
        port=settings.API_PORT,
        log_level=settings.LOG_LEVEL.lower()
    )
    server = uvicorn.Server(config)
    await server.serve()

async def main():
    """Função principal"""
    parser = argparse.ArgumentParser(description="PriceNow - Sistema de Web Scraping")
    parser.add_argument(
        "command",
        choices=["init", "scrape", "api", "list-scrapers"],
        help="Comando a ser executado"
    )
    parser.add_argument(
        "--scraper",
        help="Nome do scraper específico para executar"
    )
    parser.add_argument(
        "--all",
        action="store_true",
        help="Executar todos os scrapers"
    )
    
    args = parser.parse_args()
    
    if args.command == "init":
        logger.info("Inicializando sistema...")
        init_database()
        logger.info("Sistema inicializado com sucesso!")
    
    elif args.command == "list-scrapers":
        manager = ScrapingManager()
        scrapers = manager.get_available_scrapers()
        print("Scrapers disponíveis:")
        for scraper in scrapers:
            print(f"  - {scraper}")
    
    elif args.command == "scrape":
        init_database()  # Garante que o banco está inicializado
        manager = ScrapingManager()
        
        if args.all:
            await manager.run_all_scrapers()
        elif args.scraper:
            if args.scraper not in manager.get_available_scrapers():
                logger.error(f"Scraper '{args.scraper}' não encontrado")
                print(f"Scrapers disponíveis: {manager.get_available_scrapers()}")
                sys.exit(1)
            await manager.run_scraper(args.scraper)
        else:
            logger.error("Especifique um scraper com --scraper ou use --all")
            sys.exit(1)
    
    elif args.command == "api":
        init_database()  # Garante que o banco está inicializado
        await run_api()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Aplicação interrompida pelo usuário")
    except Exception as e:
        logger.error(f"Erro na aplicação: {e}")
        sys.exit(1)

#!/usr/bin/env python3
"""
Verifica os dados do Drogasil salvos no banco
"""

import asyncio
from database.connection import DatabaseManager
from sqlalchemy import text

async def check_drogasil_data():
    """Verifica dados do Drogasil no banco"""
    session = await DatabaseManager.get_async_session()

    try:
        # Verificar produtos do Drogasil
        result = await session.execute(text("""
            SELECT product_name, price, brand, category, additional_data, created_at
            FROM scraped_data
            WHERE additional_data->>'site_type' = 'farmacia'
            ORDER BY created_at DESC
            LIMIT 10
        """))
        rows = result.fetchall()
        
        print("📦 Produtos do Drogasil no banco:")
        print("=" * 50)

        if not rows:
            print("❌ Nenhum produto encontrado")
            return

        for i, row in enumerate(rows, 1):
            print(f"\n{i}. {row['product_name']}")
            print(f"   💰 Preço: R$ {row['price']}")
            print(f"   🏭 Marca: {row['brand']}")
            print(f"   📂 Categoria: {row['category']}")
            print(f"   🤖 Mock Data: {row['additional_data'].get('mock_data', False)}")
            print(f"   📅 Criado em: {row['created_at']}")
            
            # Dados adicionais
            additional = row['additional_data']
            if 'ean_searched' in additional:
                print(f"   🔍 EAN: {additional['ean_searched']}")
            if 'scraped_from' in additional:
                print(f"   🌐 URL: {additional['scraped_from']}")
        
        print(f"\n📊 Total de produtos: {len(rows)}")

        # Verificar jobs de scraping
        jobs_result = await session.execute(text("""
            SELECT scraper_name, status, items_scraped, created_at
            FROM scraping_jobs
            WHERE scraper_name = 'drogasil'
            ORDER BY created_at DESC
            LIMIT 5
        """))
        jobs_rows = jobs_result.fetchall()
        
        print(f"\n🔄 Jobs de scraping do Drogasil:")
        print("=" * 50)

        for job in jobs_rows:
            print(f"• Status: {job['status']}")
            print(f"  Items: {job['items_scraped']}")
            print(f"  Data: {job['created_at']}")
            print()

    except Exception as e:
        print(f"❌ Erro ao verificar dados: {e}")
    finally:
        await session.close()

if __name__ == "__main__":
    asyncio.run(check_drogasil_data())

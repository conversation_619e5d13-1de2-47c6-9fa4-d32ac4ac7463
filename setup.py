#!/usr/bin/env python3
"""
Script de configuração e instalação do PriceNow
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def run_command(command, check=True):
    """Executa um comando no shell"""
    print(f"Executando: {command}")
    try:
        result = subprocess.run(command, shell=True, check=check, capture_output=True, text=True)
        if result.stdout:
            print(result.stdout)
        return result.returncode == 0
    except subprocess.CalledProcessError as e:
        print(f"Erro ao executar comando: {e}")
        if e.stderr:
            print(f"Stderr: {e.stderr}")
        return False

def check_python_version():
    """Verifica se a versão do Python é compatível"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 ou superior é necessário")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detectado")
    return True

def check_dependencies():
    """Verifica dependências do sistema"""
    print("\n🔍 Verificando dependências...")
    
    # Verifica se o pip está disponível
    if not shutil.which("pip"):
        print("❌ pip não encontrado")
        return False
    print("✅ pip encontrado")
    
    # Verifica se o Docker está disponível (opcional)
    if shutil.which("docker"):
        print("✅ Docker encontrado")
    else:
        print("⚠️  Docker não encontrado (opcional para banco de dados)")
    
    return True

def create_virtual_environment():
    """Cria ambiente virtual se não existir"""
    print("\n🐍 Configurando ambiente virtual...")
    
    venv_path = Path("venv")
    if venv_path.exists():
        print("✅ Ambiente virtual já existe")
        return True
    
    if not run_command(f"{sys.executable} -m venv venv"):
        print("❌ Falha ao criar ambiente virtual")
        return False
    
    print("✅ Ambiente virtual criado")
    return True

def install_requirements():
    """Instala dependências Python"""
    print("\n📦 Instalando dependências Python...")
    
    # Determina o comando pip baseado no SO
    if os.name == 'nt':  # Windows
        pip_cmd = "venv\\Scripts\\pip"
    else:  # Linux/Mac
        pip_cmd = "venv/bin/pip"
    
    # Atualiza pip
    if not run_command(f"{pip_cmd} install --upgrade pip"):
        print("⚠️  Falha ao atualizar pip")
    
    # Instala dependências
    if not run_command(f"{pip_cmd} install -r requirements.txt"):
        print("❌ Falha ao instalar dependências")
        return False
    
    print("✅ Dependências instaladas")
    return True

def install_playwright():
    """Instala browsers do Playwright"""
    print("\n🎭 Instalando browsers do Playwright...")
    
    # Determina o comando python baseado no SO
    if os.name == 'nt':  # Windows
        python_cmd = "venv\\Scripts\\python"
    else:  # Linux/Mac
        python_cmd = "venv/bin/python"
    
    if not run_command(f"{python_cmd} -m playwright install"):
        print("❌ Falha ao instalar browsers do Playwright")
        return False
    
    print("✅ Browsers do Playwright instalados")
    return True

def create_env_file():
    """Cria arquivo .env se não existir"""
    print("\n⚙️  Configurando variáveis de ambiente...")
    
    env_path = Path(".env")
    if env_path.exists():
        print("✅ Arquivo .env já existe")
        return True
    
    example_path = Path(".env.example")
    if example_path.exists():
        shutil.copy(example_path, env_path)
        print("✅ Arquivo .env criado a partir do .env.example")
        print("📝 Edite o arquivo .env conforme necessário")
    else:
        print("⚠️  Arquivo .env.example não encontrado")
    
    return True

def create_logs_directory():
    """Cria diretório de logs"""
    print("\n📁 Criando diretório de logs...")
    
    logs_path = Path("logs")
    logs_path.mkdir(exist_ok=True)
    print("✅ Diretório de logs criado")
    return True

def setup_database():
    """Configura banco de dados"""
    print("\n🗄️  Configurando banco de dados...")
    
    # Verifica se Docker está disponível
    if shutil.which("docker") and shutil.which("docker-compose"):
        print("Docker encontrado. Iniciando banco de dados...")
        if run_command("docker-compose up -d postgres", check=False):
            print("✅ Banco de dados PostgreSQL iniciado")
        else:
            print("⚠️  Falha ao iniciar banco com Docker")
    else:
        print("⚠️  Docker não encontrado. Configure o banco manualmente")
        print("   Ou instale Docker e execute: docker-compose up -d postgres")
    
    return True

def initialize_database():
    """Inicializa o banco de dados"""
    print("\n🔧 Inicializando banco de dados...")
    
    # Determina o comando python baseado no SO
    if os.name == 'nt':  # Windows
        python_cmd = "venv\\Scripts\\python"
    else:  # Linux/Mac
        python_cmd = "venv/bin/python"
    
    if not run_command(f"{python_cmd} main.py init", check=False):
        print("⚠️  Falha ao inicializar banco. Execute manualmente: python main.py init")
        return False
    
    print("✅ Banco de dados inicializado")
    return True

def print_usage_instructions():
    """Imprime instruções de uso"""
    print("\n" + "="*60)
    print("🎉 INSTALAÇÃO CONCLUÍDA!")
    print("="*60)
    print("\n📋 PRÓXIMOS PASSOS:")
    print("\n1. Ative o ambiente virtual:")
    if os.name == 'nt':  # Windows
        print("   venv\\Scripts\\activate")
    else:  # Linux/Mac
        print("   source venv/bin/activate")
    
    print("\n2. Configure o arquivo .env conforme necessário")
    
    print("\n3. Comandos disponíveis:")
    print("   python main.py list-scrapers    # Lista scrapers disponíveis")
    print("   python main.py scrape --scraper mercadolivre  # Executa scraper")
    print("   python main.py scrape --all     # Executa todos os scrapers")
    print("   python main.py api              # Inicia a API")
    
    print("\n4. Acesse a API:")
    print("   http://localhost:8000/docs      # Documentação Swagger")
    print("   http://localhost:8000/redoc     # Documentação ReDoc")
    
    print("\n5. Banco de dados (se usando Docker):")
    print("   http://localhost:8080           # PgAdmin")
    print("   Email: <EMAIL>")
    print("   Senha: admin")
    
    print("\n📚 Consulte o README.md para mais informações")
    print("="*60)

def main():
    """Função principal de setup"""
    print("🚀 CONFIGURAÇÃO DO PRICENOW")
    print("="*40)
    
    # Verificações iniciais
    if not check_python_version():
        sys.exit(1)
    
    if not check_dependencies():
        sys.exit(1)
    
    # Configuração do ambiente
    steps = [
        create_virtual_environment,
        install_requirements,
        install_playwright,
        create_env_file,
        create_logs_directory,
        setup_database,
        initialize_database
    ]
    
    for step in steps:
        if not step():
            print(f"\n❌ Falha na etapa: {step.__name__}")
            print("Verifique os erros acima e tente novamente")
            sys.exit(1)
    
    print_usage_instructions()

if __name__ == "__main__":
    main()
